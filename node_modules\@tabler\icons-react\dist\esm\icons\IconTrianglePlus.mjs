/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M10.363 3.591l-8.106 13.534a1.914 1.914 0 0 0 1.636 2.871h16.214a1.914 1.914 0 0 0 1.636 -2.87l-8.106 -13.536a1.914 1.914 0 0 0 -3.274 0v.001z", "key": "svg-0" }], ["path", { "d": "M9 13h6", "key": "svg-1" }], ["path", { "d": "M12 10v6", "key": "svg-2" }]];
const IconTrianglePlus = createReactComponent("outline", "triangle-plus", "TrianglePlus", __iconNode);

export { __iconNode, IconTrianglePlus as default };
//# sourceMappingURL=IconTrianglePlus.mjs.map
