/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M22 21h-5v-5h-5v-5h-5v-5h-5", "key": "svg-0" }], ["path", { "d": "M18 3v7", "key": "svg-1" }], ["path", { "d": "M15 7l3 3l3 -3", "key": "svg-2" }]];
const IconStairsDown = createReactComponent("outline", "stairs-down", "StairsDown", __iconNode);

export { __iconNode, IconStairsDown as default };
//# sourceMappingURL=IconStairsDown.mjs.map
