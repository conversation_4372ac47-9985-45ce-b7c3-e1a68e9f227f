/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M6 21v-2a4 4 0 0 1 4 -4h2", "key": "svg-0" }], ["path", { "d": "M22 16c0 4 -2.5 6 -3.5 6s-3.5 -2 -3.5 -6c1 0 2.5 -.5 3.5 -1.5c1 1 2.5 1.5 3.5 1.5z", "key": "svg-1" }], ["path", { "d": "M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0", "key": "svg-2" }]];
const IconUserShield = createReactComponent("outline", "user-shield", "UserShield", __iconNode);

export { __iconNode, IconUserShield as default };
//# sourceMappingURL=IconUserShield.mjs.map
