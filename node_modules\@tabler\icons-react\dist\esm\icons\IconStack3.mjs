/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 2l-8 4l8 4l8 -4l-8 -4", "key": "svg-0" }], ["path", { "d": "M4 10l8 4l8 -4", "key": "svg-1" }], ["path", { "d": "M4 18l8 4l8 -4", "key": "svg-2" }], ["path", { "d": "M4 14l8 4l8 -4", "key": "svg-3" }]];
const IconStack3 = createReactComponent("outline", "stack-3", "Stack3", __iconNode);

export { __iconNode, IconStack3 as default };
//# sourceMappingURL=IconStack3.mjs.map
