/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 3m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z", "key": "svg-0" }], ["path", { "d": "M10 8h4l-4 8h4", "key": "svg-1" }]];
const IconSquareLetterZ = createReactComponent("outline", "square-letter-z", "SquareLetterZ", __iconNode);

export { __iconNode, IconSquareLetterZ as default };
//# sourceMappingURL=IconSquareLetterZ.mjs.map
