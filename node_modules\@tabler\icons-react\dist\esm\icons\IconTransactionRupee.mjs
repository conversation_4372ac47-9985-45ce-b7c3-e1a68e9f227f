/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M21 12h-6h1a3 3 0 0 1 0 6h-1l3 3", "key": "svg-0" }], ["path", { "d": "M15 15h6", "key": "svg-1" }], ["path", { "d": "M5 5m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-2" }], ["path", { "d": "M17 5m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-3" }], ["path", { "d": "M7 5h8", "key": "svg-4" }], ["path", { "d": "M7 5v8a3 3 0 0 0 3 3h1", "key": "svg-5" }]];
const IconTransactionRupee = createReactComponent("outline", "transaction-rupee", "TransactionRupee", __iconNode);

export { __iconNode, IconTransactionRupee as default };
//# sourceMappingURL=IconTransactionRupee.mjs.map
