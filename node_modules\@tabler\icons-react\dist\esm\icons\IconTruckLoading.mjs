/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M2 3h1a2 2 0 0 1 2 2v10a2 2 0 0 0 2 2h15", "key": "svg-0" }], ["path", { "d": "M9 6m0 3a3 3 0 0 1 3 -3h4a3 3 0 0 1 3 3v2a3 3 0 0 1 -3 3h-4a3 3 0 0 1 -3 -3z", "key": "svg-1" }], ["path", { "d": "M9 19m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-2" }], ["path", { "d": "M18 19m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-3" }]];
const IconTruckLoading = createReactComponent("outline", "truck-loading", "TruckLoading", __iconNode);

export { __iconNode, IconTruckLoading as default };
//# sourceMappingURL=IconTruckLoading.mjs.map
