/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M5 7l5 5l-5 5", "key": "svg-0" }], ["path", { "d": "M12 19l7 0", "key": "svg-1" }]];
const IconTerminal = createReactComponent("outline", "terminal", "Terminal", __iconNode);

export { __iconNode, IconTerminal as default };
//# sourceMappingURL=IconTerminal.mjs.map
