/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M7.149 7.144a.498 .498 0 0 0 .351 .856a.498 .498 0 0 0 .341 -.135", "key": "svg-0" }], ["path", { "d": "M3.883 3.875a2.99 2.99 0 0 0 -.883 2.125v5.172a2 2 0 0 0 .586 1.414l7.71 7.71a2.41 2.41 0 0 0 3.408 0l2.796 -2.796m2.005 -2.005l.79 -.79a2.41 2.41 0 0 0 0 -3.41l-7.71 -7.71a2 2 0 0 0 -1.412 -.585h-4.173", "key": "svg-1" }], ["path", { "d": "M3 3l18 18", "key": "svg-2" }]];
const IconTagOff = createReactComponent("outline", "tag-off", "TagOff", __iconNode);

export { __iconNode, IconTagOff as default };
//# sourceMappingURL=IconTagOff.mjs.map
