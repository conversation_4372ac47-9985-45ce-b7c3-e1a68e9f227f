/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 4a2 2 0 0 1 2 2v9", "key": "svg-0" }], ["path", { "d": "M5 6a2 2 0 0 1 4 0v9", "key": "svg-1" }], ["path", { "d": "M9 6a2 2 0 0 1 4 0v10a7 5 0 0 0 7 5", "key": "svg-2" }], ["path", { "d": "M12 21a7 5 0 0 0 7 -5v-2a3 3 0 0 0 -6 0", "key": "svg-3" }]];
const IconZodiacVirgo = createReactComponent("outline", "zodiac-virgo", "ZodiacVirgo", __iconNode);

export { __iconNode, IconZodiacVirgo as default };
//# sourceMappingURL=IconZodiacVirgo.mjs.map
