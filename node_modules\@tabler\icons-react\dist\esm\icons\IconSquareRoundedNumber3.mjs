/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M10 9a1 1 0 0 1 1 -1h2a1 1 0 0 1 1 1v2a1 1 0 0 1 -1 1h-2h2a1 1 0 0 1 1 1v2a1 1 0 0 1 -1 1h-2a1 1 0 0 1 -1 -1", "key": "svg-0" }], ["path", { "d": "M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z", "key": "svg-1" }]];
const IconSquareRoundedNumber3 = createReactComponent("outline", "square-rounded-number-3", "SquareRoundedNumber3", __iconNode);

export { __iconNode, IconSquareRoundedNumber3 as default };
//# sourceMappingURL=IconSquareRoundedNumber3.mjs.map
