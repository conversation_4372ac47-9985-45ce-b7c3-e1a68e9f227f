/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 10v3a1 1 0 0 0 1 1h14a1 1 0 0 0 1 -1v-3", "key": "svg-0" }]];
const IconSpace = createReactComponent("outline", "space", "Space", __iconNode);

export { __iconNode, IconSpace as default };
//# sourceMappingURL=IconSpace.mjs.map
