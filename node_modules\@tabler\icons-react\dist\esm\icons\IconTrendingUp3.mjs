/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M18 5l3 3l-3 3", "key": "svg-0" }], ["path", { "d": "M3 18h2.397a5 5 0 0 0 4.096 -2.133l4.014 -5.734a5 5 0 0 1 4.096 -2.133h3.397", "key": "svg-1" }]];
const IconTrendingUp3 = createReactComponent("outline", "trending-up-3", "TrendingUp3", __iconNode);

export { __iconNode, IconTrendingUp3 as default };
//# sourceMappingURL=IconTrendingUp3.mjs.map
