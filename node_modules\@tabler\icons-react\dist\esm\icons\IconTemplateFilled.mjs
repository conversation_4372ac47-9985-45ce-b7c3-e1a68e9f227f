/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M19 3a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-2a2 2 0 0 1 2 -2z", "key": "svg-0" }], ["path", { "d": "M9 11a2 2 0 0 1 2 2v6a2 2 0 0 1 -2 2h-4a2 2 0 0 1 -2 -2v-6a2 2 0 0 1 2 -2z", "key": "svg-1" }], ["path", { "d": "M20 11a1 1 0 0 1 0 2h-6a1 1 0 0 1 0 -2z", "key": "svg-2" }], ["path", { "d": "M20 15a1 1 0 0 1 0 2h-6a1 1 0 0 1 0 -2z", "key": "svg-3" }], ["path", { "d": "M20 19a1 1 0 0 1 0 2h-6a1 1 0 0 1 0 -2z", "key": "svg-4" }]];
const IconTemplateFilled = createReactComponent("filled", "template-filled", "TemplateFilled", __iconNode);

export { __iconNode, IconTemplateFilled as default };
//# sourceMappingURL=IconTemplateFilled.mjs.map
