/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M10 19h-6a1 1 0 0 1 -1 -1v-14a1 1 0 0 1 1 -1h6a2 2 0 0 1 2 2a2 2 0 0 1 2 -2h6a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-6a2 2 0 0 0 -2 2a2 2 0 0 0 -2 -2z", "key": "svg-0" }], ["path", { "d": "M12 5v16", "key": "svg-1" }], ["path", { "d": "M7 7h1", "key": "svg-2" }], ["path", { "d": "M7 11h1", "key": "svg-3" }], ["path", { "d": "M16 7h1", "key": "svg-4" }], ["path", { "d": "M16 11h1", "key": "svg-5" }], ["path", { "d": "M16 15h1", "key": "svg-6" }]];
const IconVocabulary = createReactComponent("outline", "vocabulary", "Vocabulary", __iconNode);

export { __iconNode, IconVocabulary as default };
//# sourceMappingURL=IconVocabulary.mjs.map
