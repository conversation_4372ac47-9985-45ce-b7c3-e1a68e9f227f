/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M10 8v3a1 1 0 0 0 1 1h3", "key": "svg-0" }], ["path", { "d": "M14 8v8", "key": "svg-1" }], ["path", { "d": "M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z", "key": "svg-2" }]];
const IconSquareRoundedNumber4 = createReactComponent("outline", "square-rounded-number-4", "SquareRoundedNumber4", __iconNode);

export { __iconNode, IconSquareRoundedNumber4 as default };
//# sourceMappingURL=IconSquareRoundedNumber4.mjs.map
