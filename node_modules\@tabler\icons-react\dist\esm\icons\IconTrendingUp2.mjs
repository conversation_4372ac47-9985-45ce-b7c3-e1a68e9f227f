/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M18 5l3 3l-3 3", "key": "svg-0" }], ["path", { "d": "M3 18h5l7 -10h6", "key": "svg-1" }]];
const IconTrendingUp2 = createReactComponent("outline", "trending-up-2", "TrendingUp2", __iconNode);

export { __iconNode, IconTrendingUp2 as default };
//# sourceMappingURL=IconTrendingUp2.mjs.map
