/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 12a8 8 0 0 1 16 0z", "key": "svg-0" }], ["path", { "d": "M12 12v6a2 2 0 0 0 4 0", "key": "svg-1" }]];
const IconUmbrella = createReactComponent("outline", "umbrella", "Umbrella", __iconNode);

export { __iconNode, IconUmbrella as default };
//# sourceMappingURL=IconUmbrella.mjs.map
