/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M15 11l-3 3l-3 -3", "key": "svg-0" }], ["path", { "d": "M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z", "key": "svg-1" }]];
const IconSquareRoundedChevronDown = createReactComponent("outline", "square-rounded-chevron-down", "SquareRoundedChevronDown", __iconNode);

export { __iconNode, IconSquareRoundedChevronDown as default };
//# sourceMappingURL=IconSquareRoundedChevronDown.mjs.map
