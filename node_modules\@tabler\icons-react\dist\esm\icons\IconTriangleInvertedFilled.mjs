/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M20.118 3h-16.225a2.914 2.914 0 0 0 -2.503 4.371l8.116 13.549a2.917 2.917 0 0 0 4.987 .005l8.11 -13.539a2.914 2.914 0 0 0 -2.486 -4.386z", "key": "svg-0" }]];
const IconTriangleInvertedFilled = createReactComponent("filled", "triangle-inverted-filled", "TriangleInvertedFilled", __iconNode);

export { __iconNode, IconTriangleInvertedFilled as default };
//# sourceMappingURL=IconTriangleInvertedFilled.mjs.map
