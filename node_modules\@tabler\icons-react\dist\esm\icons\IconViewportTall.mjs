/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 10v-7l3 3", "key": "svg-0" }], ["path", { "d": "M9 6l3 -3", "key": "svg-1" }], ["path", { "d": "M12 14v7l3 -3", "key": "svg-2" }], ["path", { "d": "M9 18l3 3", "key": "svg-3" }], ["path", { "d": "M18 3h1a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-1", "key": "svg-4" }], ["path", { "d": "M6 3h-1a2 2 0 0 0 -2 2v14a2 2 0 0 0 2 2h1", "key": "svg-5" }]];
const IconViewportTall = createReactComponent("outline", "viewport-tall", "ViewportTall", __iconNode);

export { __iconNode, IconViewportTall as default };
//# sourceMappingURL=IconViewportTall.mjs.map
