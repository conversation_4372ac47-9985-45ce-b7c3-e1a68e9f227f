/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 19v2h16v-14l-8 -4l-8 4v2", "key": "svg-0" }], ["path", { "d": "M13 14h-9", "key": "svg-1" }], ["path", { "d": "M7 11l-3 3l3 3", "key": "svg-2" }]];
const IconTransferOut = createReactComponent("outline", "transfer-out", "TransferOut", __iconNode);

export { __iconNode, IconTransferOut as default };
//# sourceMappingURL=IconTransferOut.mjs.map
