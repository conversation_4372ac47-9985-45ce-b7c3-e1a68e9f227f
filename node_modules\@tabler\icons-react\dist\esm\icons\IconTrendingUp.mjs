/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 17l6 -6l4 4l8 -8", "key": "svg-0" }], ["path", { "d": "M14 7l7 0l0 7", "key": "svg-1" }]];
const IconTrendingUp = createReactComponent("outline", "trending-up", "TrendingUp", __iconNode);

export { __iconNode, IconTrendingUp as default };
//# sourceMappingURL=IconTrendingUp.mjs.map
