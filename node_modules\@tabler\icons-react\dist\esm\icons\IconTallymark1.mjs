/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 5l0 14", "key": "svg-0" }]];
const IconTallymark1 = createReactComponent("outline", "tallymark-1", "Tallymark1", __iconNode);

export { __iconNode, IconTallymark1 as default };
//# sourceMappingURL=IconTallymark1.mjs.map
