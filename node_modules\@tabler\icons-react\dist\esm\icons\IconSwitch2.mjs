/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 17h5l1.67 -2.386m3.66 -5.227l1.67 -2.387h6", "key": "svg-0" }], ["path", { "d": "M18 4l3 3l-3 3", "key": "svg-1" }], ["path", { "d": "M3 7h5l7 10h6", "key": "svg-2" }], ["path", { "d": "M18 20l3 -3l-3 -3", "key": "svg-3" }]];
const IconSwitch2 = createReactComponent("outline", "switch-2", "Switch2", __iconNode);

export { __iconNode, IconSwitch2 as default };
//# sourceMappingURL=IconSwitch2.mjs.map
