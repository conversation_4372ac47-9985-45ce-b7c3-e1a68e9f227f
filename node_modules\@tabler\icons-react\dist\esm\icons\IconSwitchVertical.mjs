/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 8l4 -4l4 4", "key": "svg-0" }], ["path", { "d": "M7 4l0 9", "key": "svg-1" }], ["path", { "d": "M13 16l4 4l4 -4", "key": "svg-2" }], ["path", { "d": "M17 10l0 10", "key": "svg-3" }]];
const IconSwitchVertical = createReactComponent("outline", "switch-vertical", "SwitchVertical", __iconNode);

export { __iconNode, IconSwitchVertical as default };
//# sourceMappingURL=IconSwitchVertical.mjs.map
