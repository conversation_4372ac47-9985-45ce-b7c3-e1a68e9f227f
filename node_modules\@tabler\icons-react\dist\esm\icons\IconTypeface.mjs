/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 5a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z", "key": "svg-0" }], ["path", { "d": "M17 17a2 2 0 0 1 -2 -2v-8h-5a2 2 0 0 0 -2 2", "key": "svg-1" }], ["path", { "d": "M7 17a2.775 2.775 0 0 0 2.632 -1.897l.368 -1.103a13.4 13.4 0 0 1 3.236 -5.236l1.764 -1.764", "key": "svg-2" }], ["path", { "d": "M10 14h5", "key": "svg-3" }]];
const IconTypeface = createReactComponent("outline", "typeface", "Typeface", __iconNode);

export { __iconNode, IconTypeface as default };
//# sourceMappingURL=IconTypeface.mjs.map
