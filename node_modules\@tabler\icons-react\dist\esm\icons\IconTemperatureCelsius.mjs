/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M6 8m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-0" }], ["path", { "d": "M20 9a3 3 0 0 0 -3 -3h-1a3 3 0 0 0 -3 3v6a3 3 0 0 0 3 3h1a3 3 0 0 0 3 -3", "key": "svg-1" }]];
const IconTemperatureCelsius = createReactComponent("outline", "temperature-celsius", "TemperatureCelsius", __iconNode);

export { __iconNode, IconTemperatureCelsius as default };
//# sourceMappingURL=IconTemperatureCelsius.mjs.map
