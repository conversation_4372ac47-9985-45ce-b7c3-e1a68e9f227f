/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M19 7a3 3 0 0 1 3 3v9a3 3 0 0 1 -3 3h-9a3 3 0 0 1 -3 -3v-9a3 3 0 0 1 3 -3z", "key": "svg-0" }], ["path", { "d": "M14 2a3 3 0 0 1 3 2.999l-7 .001a5 5 0 0 0 -5 5l-.001 7l-.175 -.005a3 3 0 0 1 -2.824 -2.995v-9a3 3 0 0 1 3 -3z", "key": "svg-1" }]];
const IconSquaresFilled = createReactComponent("filled", "squares-filled", "SquaresFilled", __iconNode);

export { __iconNode, IconSquaresFilled as default };
//# sourceMappingURL=IconSquaresFilled.mjs.map
