/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M15 11l-3 3l-3 -3", "key": "svg-0" }], ["path", { "d": "M3 3m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z", "key": "svg-1" }]];
const IconSquareChevronDown = createReactComponent("outline", "square-chevron-down", "SquareChevronDown", __iconNode);

export { __iconNode, IconSquareChevronDown as default };
//# sourceMappingURL=IconSquareChevronDown.mjs.map
