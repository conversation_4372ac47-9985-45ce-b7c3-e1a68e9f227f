/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 6.28v11.44a1 1 0 0 0 1.243 .97l6 -1.5a1 1 0 0 0 .757 -.97v-8.44a1 1 0 0 0 -.757 -.97l-6 -1.5a1 1 0 0 0 -1.243 .97z", "key": "svg-0" }], ["path", { "d": "M8 6v12", "key": "svg-1" }], ["path", { "d": "M12 12h-8", "key": "svg-2" }], ["path", { "d": "M20 7l-3 5h4l-3 5", "key": "svg-3" }]];
const IconSolarElectricity = createReactComponent("outline", "solar-electricity", "SolarElectricity", __iconNode);

export { __iconNode, IconSolarElectricity as default };
//# sourceMappingURL=IconSolarElectricity.mjs.map
