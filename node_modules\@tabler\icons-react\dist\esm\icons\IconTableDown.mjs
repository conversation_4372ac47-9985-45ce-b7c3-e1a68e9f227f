/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12.5 21h-7.5a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v7.5", "key": "svg-0" }], ["path", { "d": "M3 10h18", "key": "svg-1" }], ["path", { "d": "M10 3v18", "key": "svg-2" }], ["path", { "d": "M19 16v6", "key": "svg-3" }], ["path", { "d": "M22 19l-3 3l-3 -3", "key": "svg-4" }]];
const IconTableDown = createReactComponent("outline", "table-down", "TableDown", __iconNode);

export { __iconNode, IconTableDown as default };
//# sourceMappingURL=IconTableDown.mjs.map
