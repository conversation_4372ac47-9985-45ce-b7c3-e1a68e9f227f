/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 4v16", "key": "svg-0" }], ["path", { "d": "M3 3m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z", "key": "svg-1" }], ["path", { "d": "M12 13l7.5 -7.5", "key": "svg-2" }], ["path", { "d": "M12 18l8 -8", "key": "svg-3" }], ["path", { "d": "M15 20l5 -5", "key": "svg-4" }], ["path", { "d": "M12 8l4 -4", "key": "svg-5" }]];
const IconSquareHalf = createReactComponent("outline", "square-half", "SquareHalf", __iconNode);

export { __iconNode, IconSquareHalf as default };
//# sourceMappingURL=IconSquareHalf.mjs.map
