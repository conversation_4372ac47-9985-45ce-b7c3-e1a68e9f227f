/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M6 4h12a2 2 0 0 1 2 2v7h-5a2 2 0 0 0 -2 2v5h-7a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2z", "key": "svg-0" }], ["path", { "d": "M20 13v.172a2 2 0 0 1 -.586 1.414l-4.828 4.828a2 2 0 0 1 -1.414 .586h-.172", "key": "svg-1" }]];
const IconSticker2 = createReactComponent("outline", "sticker-2", "Sticker2", __iconNode);

export { __iconNode, IconSticker2 as default };
//# sourceMappingURL=IconSticker2.mjs.map
