/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4.5 9h5m-2.5 0v6", "key": "svg-0" }], ["path", { "d": "M13 15v-6l3 4l3 -4v6", "key": "svg-1" }]];
const IconTrademark = createReactComponent("outline", "trademark", "Trademark", __iconNode);

export { __iconNode, IconTrademark as default };
//# sourceMappingURL=IconTrademark.mjs.map
