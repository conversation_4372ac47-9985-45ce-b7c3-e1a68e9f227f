/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M7 4v17m-3 -17v3a3 3 0 1 0 6 0v-3", "key": "svg-0" }], ["path", { "d": "M17 8m-3 0a3 4 0 1 0 6 0a3 4 0 1 0 -6 0", "key": "svg-1" }], ["path", { "d": "M17 12v9", "key": "svg-2" }]];
const IconToolsKitchen3 = createReactComponent("outline", "tools-kitchen-3", "ToolsKitchen3", __iconNode);

export { __iconNode, IconToolsKitchen3 as default };
//# sourceMappingURL=IconToolsKitchen3.mjs.map
