/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-0" }], ["path", { "d": "M12 7a5 5 0 1 0 5 5", "key": "svg-1" }], ["path", { "d": "M13 3.055a9 9 0 1 0 7.941 7.945", "key": "svg-2" }], ["path", { "d": "M15 6v3h3l3 -3h-3v-3z", "key": "svg-3" }], ["path", { "d": "M15 9l-3 3", "key": "svg-4" }]];
const IconTargetArrow = createReactComponent("outline", "target-arrow", "TargetArrow", __iconNode);

export { __iconNode, IconTargetArrow as default };
//# sourceMappingURL=IconTargetArrow.mjs.map
