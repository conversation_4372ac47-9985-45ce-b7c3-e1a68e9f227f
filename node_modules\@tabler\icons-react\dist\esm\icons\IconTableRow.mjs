/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 5a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-14z", "key": "svg-0" }], ["path", { "d": "M9 3l-6 6", "key": "svg-1" }], ["path", { "d": "M14 3l-7 7", "key": "svg-2" }], ["path", { "d": "M19 3l-7 7", "key": "svg-3" }], ["path", { "d": "M21 6l-4 4", "key": "svg-4" }], ["path", { "d": "M3 10h18", "key": "svg-5" }], ["path", { "d": "M10 10v11", "key": "svg-6" }]];
const IconTableRow = createReactComponent("outline", "table-row", "TableRow", __iconNode);

export { __iconNode, IconTableRow as default };
//# sourceMappingURL=IconTableRow.mjs.map
