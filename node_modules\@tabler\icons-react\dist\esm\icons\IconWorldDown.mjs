/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M20.986 12.509a9 9 0 1 0 -8.455 8.476", "key": "svg-0" }], ["path", { "d": "M3.6 9h16.8", "key": "svg-1" }], ["path", { "d": "M3.6 15h10.9", "key": "svg-2" }], ["path", { "d": "M11.5 3a17 17 0 0 0 0 18", "key": "svg-3" }], ["path", { "d": "M12.5 3c2.313 3.706 3.07 7.857 2.27 12", "key": "svg-4" }], ["path", { "d": "M19 16v6", "key": "svg-5" }], ["path", { "d": "M22 19l-3 3l-3 -3", "key": "svg-6" }]];
const IconWorldDown = createReactComponent("outline", "world-down", "WorldDown", __iconNode);

export { __iconNode, IconWorldDown as default };
//# sourceMappingURL=IconWorldDown.mjs.map
