/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M7 15m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-0" }], ["path", { "d": "M15 15a2 2 0 0 0 2 2m2 -2a2 2 0 0 0 -2 -2", "key": "svg-1" }], ["path", { "d": "M3 9c0 .552 .895 1 2 1h5m4 0h5c1.105 0 2 -.448 2 -1", "key": "svg-2" }], ["path", { "d": "M3 3l18 18", "key": "svg-3" }]];
const IconSkateboardOff = createReactComponent("outline", "skateboard-off", "SkateboardOff", __iconNode);

export { __iconNode, IconSkateboardOff as default };
//# sourceMappingURL=IconSkateboardOff.mjs.map
