/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M20 12l-2 .5a6 6 0 0 1 -6.5 -6.5l.5 -2l8 8", "key": "svg-0" }], ["path", { "d": "M20 12a8 8 0 1 1 -8 -8", "key": "svg-1" }]];
const IconSticker = createReactComponent("outline", "sticker", "Sticker", __iconNode);

export { __iconNode, IconSticker as default };
//# sourceMappingURL=IconSticker.mjs.map
