/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 9m-5 0a5 5 0 1 0 10 0a5 5 0 1 0 -10 0", "key": "svg-0" }], ["path", { "d": "M12 14l0 7", "key": "svg-1" }], ["path", { "d": "M9 18l6 0", "key": "svg-2" }]];
const IconVenus = createReactComponent("outline", "venus", "Venus", __iconNode);

export { __iconNode, IconVenus as default };
//# sourceMappingURL=IconVenus.mjs.map
