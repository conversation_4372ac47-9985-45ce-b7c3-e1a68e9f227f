/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M10 10v4a2 2 0 1 0 4 0v-4a2 2 0 1 0 -4 0z", "key": "svg-0" }], ["path", { "d": "M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z", "key": "svg-1" }]];
const IconSquareRoundedNumber0 = createReactComponent("outline", "square-rounded-number-0", "SquareRoundedNumber0", __iconNode);

export { __iconNode, IconSquareRoundedNumber0 as default };
//# sourceMappingURL=IconSquareRoundedNumber0.mjs.map
