/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 20l16 -16", "key": "svg-0" }], ["path", { "d": "M13 4h7v7", "key": "svg-1" }], ["path", { "d": "M6.5 12.5l5 5", "key": "svg-2" }]];
const IconZodiacSagittarius = createReactComponent("outline", "zodiac-sagittarius", "ZodiacSagittarius", __iconNode);

export { __iconNode, IconZodiacSagittarius as default };
//# sourceMappingURL=IconZodiacSagittarius.mjs.map
