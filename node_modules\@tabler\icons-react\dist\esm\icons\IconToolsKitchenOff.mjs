/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M7 3h5l-.5 4.5m-.4 3.595l-.1 .905h-6l-.875 -7.874", "key": "svg-0" }], ["path", { "d": "M7 18h2v3h-2z", "key": "svg-1" }], ["path", { "d": "M15.225 11.216c.42 -2.518 1.589 -5.177 4.775 -8.216v12h-1", "key": "svg-2" }], ["path", { "d": "M20 15v1m0 4v1h-1v-2", "key": "svg-3" }], ["path", { "d": "M8 12v6", "key": "svg-4" }], ["path", { "d": "M3 3l18 18", "key": "svg-5" }]];
const IconToolsKitchenOff = createReactComponent("outline", "tools-kitchen-off", "ToolsKitchenOff", __iconNode);

export { __iconNode, IconToolsKitchenOff as default };
//# sourceMappingURL=IconToolsKitchenOff.mjs.map
