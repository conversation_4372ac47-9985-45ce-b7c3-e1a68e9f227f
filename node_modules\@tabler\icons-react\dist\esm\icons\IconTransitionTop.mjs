/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M21 6a3 3 0 0 0 -3 -3h-12a3 3 0 0 0 -3 3", "key": "svg-0" }], ["path", { "d": "M6 21h12a3 3 0 0 0 0 -6h-12a3 3 0 0 0 0 6z", "key": "svg-1" }], ["path", { "d": "M12 15v-8", "key": "svg-2" }], ["path", { "d": "M9 10l3 -3l3 3", "key": "svg-3" }]];
const IconTransitionTop = createReactComponent("outline", "transition-top", "TransitionTop", __iconNode);

export { __iconNode, IconTransitionTop as default };
//# sourceMappingURL=IconTransitionTop.mjs.map
