/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M8.487 21h7.026a4 4 0 0 0 3.808 -5.224l-1.706 -5.306a5 5 0 0 0 -4.76 -3.47h-1.71a5 5 0 0 0 -4.76 3.47l-1.706 5.306a4 4 0 0 0 3.808 5.224", "key": "svg-0" }], ["path", { "d": "M15 3q -1 4 -3 4t -3 -4z", "key": "svg-1" }], ["path", { "d": "M14 11h-1a2 2 0 0 0 -2 2v2c0 1.105 -.395 2 -1.5 2h4.5", "key": "svg-2" }], ["path", { "d": "M10 14h3", "key": "svg-3" }]];
const IconTaxPound = createReactComponent("outline", "tax-pound", "TaxPound", __iconNode);

export { __iconNode, IconTaxPound as default };
//# sourceMappingURL=IconTaxPound.mjs.map
