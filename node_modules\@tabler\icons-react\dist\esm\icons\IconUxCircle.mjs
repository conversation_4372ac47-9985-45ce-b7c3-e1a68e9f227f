/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M7 10v2a2 2 0 1 0 4 0v-2", "key": "svg-1" }], ["path", { "d": "M14 10l3 4", "key": "svg-2" }], ["path", { "d": "M14 14l3 -4", "key": "svg-3" }]];
const IconUxCircle = createReactComponent("outline", "ux-circle", "UxCircle", __iconNode);

export { __iconNode, IconUxCircle as default };
//# sourceMappingURL=IconUxCircle.mjs.map
