/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0", "key": "svg-0" }], ["path", { "d": "M6 21v-2a4 4 0 0 1 4 -4h3.5", "key": "svg-1" }], ["path", { "d": "M20 21l2 -2l-2 -2", "key": "svg-2" }], ["path", { "d": "M17 17l-2 2l2 2", "key": "svg-3" }]];
const IconUserCode = createReactComponent("outline", "user-code", "UserCode", __iconNode);

export { __iconNode, IconUserCode as default };
//# sourceMappingURL=IconUserCode.mjs.map
