/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12.5 21c-.18 .002 -.314 0 -.5 0c-7.2 0 -9 -1.8 -9 -9s1.8 -9 9 -9s9 1.8 9 9c0 1.136 -.046 2.138 -.152 3.02", "key": "svg-0" }], ["path", { "d": "M16 19h6", "key": "svg-1" }]];
const IconSquareRoundedMinus2 = createReactComponent("outline", "square-rounded-minus-2", "SquareRoundedMinus2", __iconNode);

export { __iconNode, IconSquareRoundedMinus2 as default };
//# sourceMappingURL=IconSquareRoundedMinus2.mjs.map
