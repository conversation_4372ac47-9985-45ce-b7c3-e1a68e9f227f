/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M10 8l4 8", "key": "svg-0" }], ["path", { "d": "M10 16l4 -8", "key": "svg-1" }], ["path", { "d": "M17 8l4 8", "key": "svg-2" }], ["path", { "d": "M17 16l4 -8", "key": "svg-3" }], ["path", { "d": "M3 8l4 8", "key": "svg-4" }], ["path", { "d": "M3 16l4 -8", "key": "svg-5" }]];
const IconXxx = createReactComponent("outline", "xxx", "Xxx", __iconNode);

export { __iconNode, IconXxx as default };
//# sourceMappingURL=IconXxx.mjs.map
