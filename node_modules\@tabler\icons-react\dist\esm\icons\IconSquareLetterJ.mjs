/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 3m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z", "key": "svg-0" }], ["path", { "d": "M10 8h4v6a2 2 0 1 1 -4 0", "key": "svg-1" }]];
const IconSquareLetterJ = createReactComponent("outline", "square-letter-j", "SquareLetterJ", __iconNode);

export { __iconNode, IconSquareLetterJ as default };
//# sourceMappingURL=IconSquareLetterJ.mjs.map
