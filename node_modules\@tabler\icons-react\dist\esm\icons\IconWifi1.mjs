/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 18l.01 0", "key": "svg-0" }], ["path", { "d": "M9.172 15.172a4 4 0 0 1 5.656 0", "key": "svg-1" }]];
const IconWifi1 = createReactComponent("outline", "wifi-1", "Wifi1", __iconNode);

export { __iconNode, IconWifi1 as default };
//# sourceMappingURL=IconWifi1.mjs.map
