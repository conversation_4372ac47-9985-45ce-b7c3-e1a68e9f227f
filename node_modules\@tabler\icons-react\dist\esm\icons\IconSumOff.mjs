/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M18 18a1 1 0 0 1 -1 1h-11l6 -7m-3 -7h8a1 1 0 0 1 1 1v2", "key": "svg-0" }], ["path", { "d": "M3 3l18 18", "key": "svg-1" }]];
const IconSumOff = createReactComponent("outline", "sum-off", "SumOff", __iconNode);

export { __iconNode, IconSumOff as default };
//# sourceMappingURL=IconSumOff.mjs.map
