/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M21 8h-4v8h4", "key": "svg-0" }], ["path", { "d": "M17 12h2.5", "key": "svg-1" }], ["path", { "d": "M4 8v8h4", "key": "svg-2" }], ["path", { "d": "M10 8h4", "key": "svg-3" }], ["path", { "d": "M12 8v8", "key": "svg-4" }]];
const IconSignalLte = createReactComponent("outline", "signal-lte", "SignalLte", __iconNode);

export { __iconNode, IconSignalLte as default };
//# sourceMappingURL=IconSignalLte.mjs.map
