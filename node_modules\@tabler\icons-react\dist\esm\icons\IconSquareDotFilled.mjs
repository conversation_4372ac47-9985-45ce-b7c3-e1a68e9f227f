/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M19 2a3 3 0 0 1 3 3v14a3 3 0 0 1 -3 3h-14a3 3 0 0 1 -3 -3v-14a3 3 0 0 1 3 -3zm-7 8a2 2 0 0 0 -1.995 1.85l-.005 .15l.005 .15a2 2 0 1 0 1.995 -2.15z", "key": "svg-0" }]];
const IconSquareDotFilled = createReactComponent("filled", "square-dot-filled", "SquareDotFilled", __iconNode);

export { __iconNode, IconSquareDotFilled as default };
//# sourceMappingURL=IconSquareDotFilled.mjs.map
