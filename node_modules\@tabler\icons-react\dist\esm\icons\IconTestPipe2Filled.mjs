/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M16 2a1 1 0 0 1 0 2v14a4 4 0 1 1 -8 0v-14a1 1 0 1 1 0 -2zm-2 2h-4v7h4z", "key": "svg-0" }]];
const IconTestPipe2Filled = createReactComponent("filled", "test-pipe-2-filled", "TestPipe2Filled", __iconNode);

export { __iconNode, IconTestPipe2Filled as default };
//# sourceMappingURL=IconTestPipe2Filled.mjs.map
