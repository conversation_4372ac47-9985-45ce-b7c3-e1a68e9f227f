/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 3a4 4 0 0 1 2.906 6.75a6 6 0 1 1 -5.81 0a4 4 0 0 1 2.904 -6.75z", "key": "svg-0" }], ["path", { "d": "M17.5 11.5l2.5 -1.5", "key": "svg-1" }], ["path", { "d": "M6.5 11.5l-2.5 -1.5", "key": "svg-2" }], ["path", { "d": "M12 13h.01", "key": "svg-3" }], ["path", { "d": "M12 16h.01", "key": "svg-4" }]];
const IconSnowman = createReactComponent("outline", "snowman", "Snowman", __iconNode);

export { __iconNode, IconSnowman as default };
//# sourceMappingURL=IconSnowman.mjs.map
