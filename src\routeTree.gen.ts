/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as SettingsRouteImport } from './routes/settings'
import { Route as PlaygroundRouteImport } from './routes/playground'
import { Route as ModelsRouteImport } from './routes/models'
import { Route as DocumentationRouteImport } from './routes/documentation'
import { Route as IndexRouteImport } from './routes/index'
import { Route as PlaygroundSettingsRouteImport } from './routes/playground.settings'

const SettingsRoute = SettingsRouteImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => rootRouteImport,
} as any)
const PlaygroundRoute = PlaygroundRouteImport.update({
  id: '/playground',
  path: '/playground',
  getParentRoute: () => rootRouteImport,
} as any)
const ModelsRoute = ModelsRouteImport.update({
  id: '/models',
  path: '/models',
  getParentRoute: () => rootRouteImport,
} as any)
const DocumentationRoute = DocumentationRouteImport.update({
  id: '/documentation',
  path: '/documentation',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const PlaygroundSettingsRoute = PlaygroundSettingsRouteImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => PlaygroundRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/documentation': typeof DocumentationRoute
  '/models': typeof ModelsRoute
  '/playground': typeof PlaygroundRouteWithChildren
  '/settings': typeof SettingsRoute
  '/playground/settings': typeof PlaygroundSettingsRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/documentation': typeof DocumentationRoute
  '/models': typeof ModelsRoute
  '/playground': typeof PlaygroundRouteWithChildren
  '/settings': typeof SettingsRoute
  '/playground/settings': typeof PlaygroundSettingsRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/documentation': typeof DocumentationRoute
  '/models': typeof ModelsRoute
  '/playground': typeof PlaygroundRouteWithChildren
  '/settings': typeof SettingsRoute
  '/playground/settings': typeof PlaygroundSettingsRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/documentation'
    | '/models'
    | '/playground'
    | '/settings'
    | '/playground/settings'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/documentation'
    | '/models'
    | '/playground'
    | '/settings'
    | '/playground/settings'
  id:
    | '__root__'
    | '/'
    | '/documentation'
    | '/models'
    | '/playground'
    | '/settings'
    | '/playground/settings'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  DocumentationRoute: typeof DocumentationRoute
  ModelsRoute: typeof ModelsRoute
  PlaygroundRoute: typeof PlaygroundRouteWithChildren
  SettingsRoute: typeof SettingsRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/settings': {
      id: '/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof SettingsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/playground': {
      id: '/playground'
      path: '/playground'
      fullPath: '/playground'
      preLoaderRoute: typeof PlaygroundRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/models': {
      id: '/models'
      path: '/models'
      fullPath: '/models'
      preLoaderRoute: typeof ModelsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/documentation': {
      id: '/documentation'
      path: '/documentation'
      fullPath: '/documentation'
      preLoaderRoute: typeof DocumentationRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/playground/settings': {
      id: '/playground/settings'
      path: '/settings'
      fullPath: '/playground/settings'
      preLoaderRoute: typeof PlaygroundSettingsRouteImport
      parentRoute: typeof PlaygroundRoute
    }
  }
}

interface PlaygroundRouteChildren {
  PlaygroundSettingsRoute: typeof PlaygroundSettingsRoute
}

const PlaygroundRouteChildren: PlaygroundRouteChildren = {
  PlaygroundSettingsRoute: PlaygroundSettingsRoute,
}

const PlaygroundRouteWithChildren = PlaygroundRoute._addFileChildren(
  PlaygroundRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  DocumentationRoute: DocumentationRoute,
  ModelsRoute: ModelsRoute,
  PlaygroundRoute: PlaygroundRouteWithChildren,
  SettingsRoute: SettingsRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
