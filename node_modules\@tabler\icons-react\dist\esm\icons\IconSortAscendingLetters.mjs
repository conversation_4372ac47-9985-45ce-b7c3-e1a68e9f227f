/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M15 10v-5c0 -1.38 .62 -2 2 -2s2 .62 2 2v5m0 -3h-4", "key": "svg-0" }], ["path", { "d": "M19 21h-4l4 -7h-4", "key": "svg-1" }], ["path", { "d": "M4 15l3 3l3 -3", "key": "svg-2" }], ["path", { "d": "M7 6v12", "key": "svg-3" }]];
const IconSortAscendingLetters = createReactComponent("outline", "sort-ascending-letters", "SortAscendingLetters", __iconNode);

export { __iconNode, IconSortAscendingLetters as default };
//# sourceMappingURL=IconSortAscendingLetters.mjs.map
