/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M14 20a2 2 0 1 0 -4 0a2 2 0 0 0 4 0z", "key": "svg-0" }], ["path", { "d": "M14 4a2 2 0 1 0 -4 0a2 2 0 0 0 4 0z", "key": "svg-1" }], ["path", { "d": "M6 12a2 2 0 1 0 -4 0a2 2 0 0 0 4 0z", "key": "svg-2" }], ["path", { "d": "M22 12a2 2 0 1 0 -4 0a2 2 0 0 0 4 0z", "key": "svg-3" }], ["path", { "d": "M13.5 5.5l5 5", "key": "svg-4" }], ["path", { "d": "M5.5 13.5l5 5", "key": "svg-5" }], ["path", { "d": "M13.5 18.5l5 -5", "key": "svg-6" }], ["path", { "d": "M10.5 5.5l-5 5", "key": "svg-7" }]];
const IconTopologyRing = createReactComponent("outline", "topology-ring", "TopologyRing", __iconNode);

export { __iconNode, IconTopologyRing as default };
//# sourceMappingURL=IconTopologyRing.mjs.map
