/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 4m0 1a1 1 0 0 1 1 -1h14a1 1 0 0 1 1 1v2a1 1 0 0 1 -1 1h-14a1 1 0 0 1 -1 -1z", "key": "svg-0" }], ["path", { "d": "M4 12m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v6a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z", "key": "svg-1" }], ["path", { "d": "M14 12l6 0", "key": "svg-2" }], ["path", { "d": "M14 16l6 0", "key": "svg-3" }], ["path", { "d": "M14 20l6 0", "key": "svg-4" }]];
const IconTemplate = createReactComponent("outline", "template", "Template", __iconNode);

export { __iconNode, IconTemplate as default };
//# sourceMappingURL=IconTemplate.mjs.map
