/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M14 12l6 -3l-8 -4l-8 4l6 3", "key": "svg-0" }], ["path", { "d": "M10 12l-6 3l8 4l8 -4l-6 -3l-2 1z", "fill": "currentColor", "key": "svg-1" }]];
const IconStackBackward = createReactComponent("outline", "stack-backward", "StackBackward", __iconNode);

export { __iconNode, IconStackBackward as default };
//# sourceMappingURL=IconStackBackward.mjs.map
