/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 2a5 5 0 1 1 -5 5l.005 -.217a5 5 0 0 1 4.995 -4.783z", "key": "svg-0" }], ["path", { "d": "M14 14a5 5 0 0 1 5 5v1a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2v-1a5 5 0 0 1 5 -5h4z", "key": "svg-1" }]];
const IconUserFilled = createReactComponent("filled", "user-filled", "UserFilled", __iconNode);

export { __iconNode, IconUserFilled as default };
//# sourceMappingURL=IconUserFilled.mjs.map
