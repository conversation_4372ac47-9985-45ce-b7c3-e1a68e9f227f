/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M15 4l4 0l0 4", "key": "svg-0" }], ["path", { "d": "M14.75 9.25l4.25 -5.25", "key": "svg-1" }], ["path", { "d": "M5 19l4 -4", "key": "svg-2" }], ["path", { "d": "M15 19l4 0l0 -4", "key": "svg-3" }], ["path", { "d": "M5 5l14 14", "key": "svg-4" }]];
const IconSwitch = createReactComponent("outline", "switch", "Switch", __iconNode);

export { __iconNode, IconSwitch as default };
//# sourceMappingURL=IconSwitch.mjs.map
