/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M20 10h-16l5.5 -6", "key": "svg-0" }], ["path", { "d": "M4 14h16l-5.5 6", "key": "svg-1" }]];
const IconTransfer = createReactComponent("outline", "transfer", "Transfer", __iconNode);

export { __iconNode, IconTransfer as default };
//# sourceMappingURL=IconTransfer.mjs.map
