/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 6h2.397a5 5 0 0 1 4.096 2.133l4.014 5.734a5 5 0 0 0 4.096 2.133h3.397", "key": "svg-0" }], ["path", { "d": "M18 19l3 -3l-3 -3", "key": "svg-1" }]];
const IconTrendingDown3 = createReactComponent("outline", "trending-down-3", "TrendingDown3", __iconNode);

export { __iconNode, IconTrendingDown3 as default };
//# sourceMappingURL=IconTrendingDown3.mjs.map
