/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M15 8l-3 3l-3 -3", "key": "svg-0" }], ["path", { "d": "M15 13l-3 3l-3 -3", "key": "svg-1" }], ["path", { "d": "M3 5a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-14z", "key": "svg-2" }]];
const IconSquareChevronsDown = createReactComponent("outline", "square-chevrons-down", "SquareChevronsDown", __iconNode);

export { __iconNode, IconSquareChevronsDown as default };
//# sourceMappingURL=IconSquareChevronsDown.mjs.map
