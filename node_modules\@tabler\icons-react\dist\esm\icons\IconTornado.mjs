/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M21 4l-18 0", "key": "svg-0" }], ["path", { "d": "M13 16l-6 0", "key": "svg-1" }], ["path", { "d": "M11 20l4 0", "key": "svg-2" }], ["path", { "d": "M6 8l14 0", "key": "svg-3" }], ["path", { "d": "M4 12l12 0", "key": "svg-4" }]];
const IconTornado = createReactComponent("outline", "tornado", "Tornado", __iconNode);

export { __iconNode, IconTornado as default };
//# sourceMappingURL=IconTornado.mjs.map
