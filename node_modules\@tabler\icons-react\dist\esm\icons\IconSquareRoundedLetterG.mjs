/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M14 8h-2a2 2 0 0 0 -2 2v4a2 2 0 0 0 2 2h2v-4h-1", "key": "svg-0" }], ["path", { "d": "M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z", "key": "svg-1" }]];
const IconSquareRoundedLetterG = createReactComponent("outline", "square-rounded-letter-g", "SquareRoundedLetterG", __iconNode);

export { __iconNode, IconSquareRoundedLetterG as default };
//# sourceMappingURL=IconSquareRoundedLetterG.mjs.map
