/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0", "key": "svg-0" }], ["path", { "d": "M6 21v-2a4 4 0 0 1 4 -4h4c.267 0 .529 .026 .781 .076", "key": "svg-1" }], ["path", { "d": "M19 16l-2 3h4l-2 3", "key": "svg-2" }]];
const IconUserBolt = createReactComponent("outline", "user-bolt", "UserBolt", __iconNode);

export { __iconNode, IconUserBolt as default };
//# sourceMappingURL=IconUserBolt.mjs.map
