/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 4a4 4 0 1 1 0 8a4 4 0 0 1 0 -8z", "key": "svg-0" }], ["path", { "d": "M12 12v8", "key": "svg-1" }], ["path", { "d": "M9 17l3 3l3 -3", "key": "svg-2" }]];
const IconSwipeDown = createReactComponent("outline", "swipe-down", "SwipeDown", __iconNode);

export { __iconNode, IconSwipeDown as default };
//# sourceMappingURL=IconSwipeDown.mjs.map
