/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 11v6h2l1 -1.5l3 1.5h10a3 3 0 0 0 0 -6h-10h0l-3 1.5l-1 -1.5h-2z", "key": "svg-0" }], ["path", { "d": "M17 11l-1 -3h-5l-1 3", "key": "svg-1" }], ["path", { "d": "M13 8v-2a1 1 0 0 1 1 -1h1", "key": "svg-2" }]];
const IconSubmarine = createReactComponent("outline", "submarine", "Submarine", __iconNode);

export { __iconNode, IconSubmarine as default };
//# sourceMappingURL=IconSubmarine.mjs.map
