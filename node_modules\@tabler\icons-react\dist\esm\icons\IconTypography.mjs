/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 20l3 0", "key": "svg-0" }], ["path", { "d": "M14 20l7 0", "key": "svg-1" }], ["path", { "d": "M6.9 15l6.9 0", "key": "svg-2" }], ["path", { "d": "M10.2 6.3l5.8 13.7", "key": "svg-3" }], ["path", { "d": "M5 20l6 -16l2 0l7 16", "key": "svg-4" }]];
const IconTypography = createReactComponent("outline", "typography", "Typography", __iconNode);

export { __iconNode, IconTypography as default };
//# sourceMappingURL=IconTypography.mjs.map
