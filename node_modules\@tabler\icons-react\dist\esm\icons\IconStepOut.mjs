/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 3l0 12", "key": "svg-0" }], ["path", { "d": "M16 7l-4 -4", "key": "svg-1" }], ["path", { "d": "M8 7l4 -4", "key": "svg-2" }], ["path", { "d": "M12 20m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-3" }]];
const IconStepOut = createReactComponent("outline", "step-out", "StepOut", __iconNode);

export { __iconNode, IconStepOut as default };
//# sourceMappingURL=IconStepOut.mjs.map
