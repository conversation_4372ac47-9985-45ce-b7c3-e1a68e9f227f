/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 16a1 1 0 0 1 0 2h-1a1 1 0 0 1 0 -2z", "key": "svg-0" }], ["path", { "d": "M12 12a5 5 0 0 1 5 5a1 1 0 0 1 -1 1h-8a1 1 0 0 1 -1 -1a5 5 0 0 1 5 -5", "key": "svg-1" }], ["path", { "d": "M21 16a1 1 0 0 1 0 2h-1a1 1 0 0 1 0 -2z", "key": "svg-2" }], ["path", { "d": "M6.307 9.893l.7 .7a1 1 0 0 1 -1.414 1.414l-.7 -.7a1 1 0 0 1 1.414 -1.414", "key": "svg-3" }], ["path", { "d": "M19.107 9.893a1 1 0 0 1 0 1.414l-.7 .7a1 1 0 0 1 -1.414 -1.414l.7 -.7a1 1 0 0 1 1.414 0", "key": "svg-4" }], ["path", { "d": "M12.707 2.293l3 3a1 1 0 1 1 -1.414 1.414l-1.293 -1.292v3.585a1 1 0 0 1 -.883 .993l-.117 .007a1 1 0 0 1 -1 -1v-3.586l-1.293 1.293a1 1 0 0 1 -1.414 -1.414l2.958 -2.96a1 1 0 0 1 .15 -.135l.127 -.08l.068 -.033l.11 -.041l.12 -.029c.3 -.055 .627 .024 .881 .278", "key": "svg-5" }], ["path", { "d": "M3 20h18a1 1 0 0 1 0 2h-18a1 1 0 0 1 0 -2", "key": "svg-6" }], ["path", { "d": "M12 12a5 5 0 0 1 4.583 7.002h-9.166a5 5 0 0 1 4.583 -7.002", "key": "svg-7" }]];
const IconSunriseFilled = createReactComponent("filled", "sunrise-filled", "SunriseFilled", __iconNode);

export { __iconNode, IconSunriseFilled as default };
//# sourceMappingURL=IconSunriseFilled.mjs.map
