/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 10l3 -3l3 3l3 -3l3 3l3 -3l3 3", "key": "svg-0" }], ["path", { "d": "M3 17l3 -3l3 3l3 -3l3 3l3 -3l3 3", "key": "svg-1" }]];
const IconZodiacAquarius = createReactComponent("outline", "zodiac-aquarius", "ZodiacAquarius", __iconNode);

export { __iconNode, IconZodiacAquarius as default };
//# sourceMappingURL=IconZodiacAquarius.mjs.map
