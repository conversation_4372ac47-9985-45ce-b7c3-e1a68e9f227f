/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M13 4m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-0" }], ["path", { "d": "M7 21l3 -4", "key": "svg-1" }], ["path", { "d": "M16 21l-2 -4l-3 -3l1 -6", "key": "svg-2" }], ["path", { "d": "M6 12l2 -3l4 -1l3 3l3 1", "key": "svg-3" }]];
const IconWalk = createReactComponent("outline", "walk", "Walk", __iconNode);

export { __iconNode, IconWalk as default };
//# sourceMappingURL=IconWalk.mjs.map
