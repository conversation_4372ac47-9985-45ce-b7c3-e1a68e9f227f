/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0", "key": "svg-0" }], ["path", { "d": "M6 21v-2a4 4 0 0 1 4 -4h3", "key": "svg-1" }], ["path", { "d": "M16 22l5 -5", "key": "svg-2" }], ["path", { "d": "M21 21.5v-4.5h-4.5", "key": "svg-3" }]];
const IconUserShare = createReactComponent("outline", "user-share", "UserShare", __iconNode);

export { __iconNode, IconUserShare as default };
//# sourceMappingURL=IconUserShare.mjs.map
