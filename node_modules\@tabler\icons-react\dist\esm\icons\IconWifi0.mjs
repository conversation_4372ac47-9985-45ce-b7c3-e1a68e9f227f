/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 18l.01 0", "key": "svg-0" }]];
const IconWifi0 = createReactComponent("outline", "wifi-0", "Wifi0", __iconNode);

export { __iconNode, IconWifi0 as default };
//# sourceMappingURL=IconWifi0.mjs.map
