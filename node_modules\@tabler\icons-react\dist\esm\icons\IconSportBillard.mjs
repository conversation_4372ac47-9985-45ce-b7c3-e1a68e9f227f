/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 10m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-0" }], ["path", { "d": "M12 14m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-1" }], ["path", { "d": "M12 12m-8 0a8 8 0 1 0 16 0a8 8 0 1 0 -16 0", "key": "svg-2" }]];
const IconSportBillard = createReactComponent("outline", "sport-billard", "SportBillard", __iconNode);

export { __iconNode, IconSportBillard as default };
//# sourceMappingURL=IconSportBillard.mjs.map
