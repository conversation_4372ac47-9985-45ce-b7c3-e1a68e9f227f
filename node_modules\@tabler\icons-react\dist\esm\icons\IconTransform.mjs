/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 6a3 3 0 1 0 6 0a3 3 0 0 0 -6 0", "key": "svg-0" }], ["path", { "d": "M21 11v-3a2 2 0 0 0 -2 -2h-6l3 3m0 -6l-3 3", "key": "svg-1" }], ["path", { "d": "M3 13v3a2 2 0 0 0 2 2h6l-3 -3m0 6l3 -3", "key": "svg-2" }], ["path", { "d": "M15 18a3 3 0 1 0 6 0a3 3 0 0 0 -6 0", "key": "svg-3" }]];
const IconTransform = createReactComponent("outline", "transform", "Transform", __iconNode);

export { __iconNode, IconTransform as default };
//# sourceMappingURL=IconTransform.mjs.map
