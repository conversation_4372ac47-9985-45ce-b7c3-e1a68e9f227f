import { createFileRoute } from "@tanstack/react-router";

export const Route = createFileRoute("/settings")({
  component: SettingsComponent,
});

function SettingsComponent() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">
          Manage your application preferences and configuration.
        </p>
      </div>
      
      <div className="grid gap-6">
        <div className="rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">General</h3>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium block mb-2">Application Name</label>
              <input 
                type="text" 
                defaultValue="My Application" 
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium block mb-2">Theme</label>
              <select className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm">
                <option value="light">Light</option>
                <option value="dark">Dark</option>
                <option value="system">System</option>
              </select>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Enable Notifications</label>
                <p className="text-xs text-muted-foreground">Receive updates and alerts</p>
              </div>
              <input type="checkbox" className="rounded" defaultChecked />
            </div>
          </div>
        </div>
        
        <div className="rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">Privacy & Security</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Two-Factor Authentication</label>
                <p className="text-xs text-muted-foreground">Add an extra layer of security</p>
              </div>
              <button className="rounded-md border border-input bg-background px-3 py-1 text-sm hover:bg-accent hover:text-accent-foreground">
                Enable
              </button>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Data Collection</label>
                <p className="text-xs text-muted-foreground">Help improve the application</p>
              </div>
              <input type="checkbox" className="rounded" />
            </div>
          </div>
        </div>
        
        <div className="rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">Advanced</h3>
          <div className="space-y-4">
            <button className="rounded-md border border-destructive bg-background px-4 py-2 text-sm text-destructive hover:bg-destructive hover:text-destructive-foreground">
              Reset to Defaults
            </button>
            <button className="rounded-md border border-destructive bg-background px-4 py-2 text-sm text-destructive hover:bg-destructive hover:text-destructive-foreground">
              Clear All Data
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
