/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M16 5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-0" }], ["path", { "d": "M5 20l5 -.5l1 -2", "key": "svg-1" }], ["path", { "d": "M18 20v-5h-5.5l2.5 -6.5l-5.5 1l1.5 2", "key": "svg-2" }]];
const IconStretching = createReactComponent("outline", "stretching", "Stretching", __iconNode);

export { __iconNode, IconStretching as default };
//# sourceMappingURL=IconStretching.mjs.map
