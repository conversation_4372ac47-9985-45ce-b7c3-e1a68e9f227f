/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M16 3.937a9 9 0 1 0 5 8.063", "key": "svg-0" }], ["path", { "d": "M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-1" }], ["path", { "d": "M20 4m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-2" }], ["path", { "d": "M20 4l-3.5 10l-2.5 2", "key": "svg-3" }]];
const IconVinyl = createReactComponent("outline", "vinyl", "Vinyl", __iconNode);

export { __iconNode, IconVinyl as default };
//# sourceMappingURL=IconVinyl.mjs.map
