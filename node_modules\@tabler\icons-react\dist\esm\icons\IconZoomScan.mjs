/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 8v-2a2 2 0 0 1 2 -2h2", "key": "svg-0" }], ["path", { "d": "M4 16v2a2 2 0 0 0 2 2h2", "key": "svg-1" }], ["path", { "d": "M16 4h2a2 2 0 0 1 2 2v2", "key": "svg-2" }], ["path", { "d": "M16 20h2a2 2 0 0 0 2 -2v-2", "key": "svg-3" }], ["path", { "d": "M8 11a3 3 0 1 0 6 0a3 3 0 0 0 -6 0", "key": "svg-4" }], ["path", { "d": "M16 16l-2.5 -2.5", "key": "svg-5" }]];
const IconZoomScan = createReactComponent("outline", "zoom-scan", "ZoomScan", __iconNode);

export { __iconNode, IconZoomScan as default };
//# sourceMappingURL=IconZoomScan.mjs.map
