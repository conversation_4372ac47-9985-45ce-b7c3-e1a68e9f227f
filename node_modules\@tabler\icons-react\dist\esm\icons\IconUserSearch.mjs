/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0", "key": "svg-0" }], ["path", { "d": "M6 21v-2a4 4 0 0 1 4 -4h1.5", "key": "svg-1" }], ["path", { "d": "M18 18m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-2" }], ["path", { "d": "M20.2 20.2l1.8 1.8", "key": "svg-3" }]];
const IconUserSearch = createReactComponent("outline", "user-search", "UserSearch", __iconNode);

export { __iconNode, IconUserSearch as default };
//# sourceMappingURL=IconUserSearch.mjs.map
