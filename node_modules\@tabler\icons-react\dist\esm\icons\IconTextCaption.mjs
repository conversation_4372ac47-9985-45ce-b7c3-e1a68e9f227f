/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 15h16", "key": "svg-0" }], ["path", { "d": "M4 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z", "key": "svg-1" }], ["path", { "d": "M4 20h12", "key": "svg-2" }]];
const IconTextCaption = createReactComponent("outline", "text-caption", "TextCaption", __iconNode);

export { __iconNode, IconTextCaption as default };
//# sourceMappingURL=IconTextCaption.mjs.map
