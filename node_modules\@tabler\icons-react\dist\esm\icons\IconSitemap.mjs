/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 15m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z", "key": "svg-0" }], ["path", { "d": "M15 15m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z", "key": "svg-1" }], ["path", { "d": "M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z", "key": "svg-2" }], ["path", { "d": "M6 15v-1a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v1", "key": "svg-3" }], ["path", { "d": "M12 9l0 3", "key": "svg-4" }]];
const IconSitemap = createReactComponent("outline", "sitemap", "Sitemap", __iconNode);

export { __iconNode, IconSitemap as default };
//# sourceMappingURL=IconSitemap.mjs.map
