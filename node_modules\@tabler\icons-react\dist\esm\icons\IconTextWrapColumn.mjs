/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M7 9h7a3 3 0 0 1 0 6h-4l2 -2", "key": "svg-0" }], ["path", { "d": "M12 17l-2 -2", "key": "svg-1" }], ["path", { "d": "M3 3v18", "key": "svg-2" }], ["path", { "d": "M21 3v18", "key": "svg-3" }]];
const IconTextWrapColumn = createReactComponent("outline", "text-wrap-column", "TextWrapColumn", __iconNode);

export { __iconNode, IconTextWrapColumn as default };
//# sourceMappingURL=IconTextWrapColumn.mjs.map
