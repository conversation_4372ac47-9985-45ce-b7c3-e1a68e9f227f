/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M7 9.5l-3 1.5l8 4l8 -4l-3 -1.5", "key": "svg-0" }], ["path", { "d": "M4 15l8 4l8 -4", "key": "svg-1" }], ["path", { "d": "M12 11v-7", "key": "svg-2" }], ["path", { "d": "M9 7l3 -3l3 3", "key": "svg-3" }]];
const IconStackPop = createReactComponent("outline", "stack-pop", "StackPop", __iconNode);

export { __iconNode, IconStackPop as default };
//# sourceMappingURL=IconStackPop.mjs.map
