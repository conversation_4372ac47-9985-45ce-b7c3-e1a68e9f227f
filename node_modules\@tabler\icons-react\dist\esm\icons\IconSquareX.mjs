/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 5a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-14z", "key": "svg-0" }], ["path", { "d": "M9 9l6 6m0 -6l-6 6", "key": "svg-1" }]];
const IconSquareX = createReactComponent("outline", "square-x", "SquareX", __iconNode);

export { __iconNode, IconSquareX as default };
//# sourceMappingURL=IconSquareX.mjs.map
