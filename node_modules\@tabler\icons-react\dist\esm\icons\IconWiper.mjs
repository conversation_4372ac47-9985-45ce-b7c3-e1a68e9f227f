/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 18m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-0" }], ["path", { "d": "M3 9l5.5 5.5a5 5 0 0 1 7 0l5.5 -5.5a12 12 0 0 0 -18 0", "key": "svg-1" }], ["path", { "d": "M12 18l-2.2 -12.8", "key": "svg-2" }]];
const IconWiper = createReactComponent("outline", "wiper", "Wiper", __iconNode);

export { __iconNode, IconWiper as default };
//# sourceMappingURL=IconWiper.mjs.map
