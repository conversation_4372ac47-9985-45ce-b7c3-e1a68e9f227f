/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M16 12l-4 -4l-4 4", "key": "svg-0" }], ["path", { "d": "M12 16v-8", "key": "svg-1" }], ["path", { "d": "M3 3m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z", "key": "svg-2" }]];
const IconSquareArrowUp = createReactComponent("outline", "square-arrow-up", "SquareArrowUp", __iconNode);

export { __iconNode, IconSquareArrowUp as default };
//# sourceMappingURL=IconSquareArrowUp.mjs.map
