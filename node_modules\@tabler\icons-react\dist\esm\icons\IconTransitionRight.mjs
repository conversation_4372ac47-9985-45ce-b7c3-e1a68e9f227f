/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M18 3a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3", "key": "svg-0" }], ["path", { "d": "M3 18v-12a3 3 0 1 1 6 0v12a3 3 0 0 1 -6 0z", "key": "svg-1" }], ["path", { "d": "M9 12h8", "key": "svg-2" }], ["path", { "d": "M14 15l3 -3l-3 -3", "key": "svg-3" }]];
const IconTransitionRight = createReactComponent("outline", "transition-right", "TransitionRight", __iconNode);

export { __iconNode, IconTransitionRight as default };
//# sourceMappingURL=IconTransitionRight.mjs.map
