/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 11h18", "key": "svg-0" }], ["path", { "d": "M5 11v-4a3 3 0 0 1 3 -3h8a3 3 0 0 1 3 3v4", "key": "svg-1" }], ["path", { "d": "M7 17m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-2" }], ["path", { "d": "M17 17m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-3" }], ["path", { "d": "M10 17h4", "key": "svg-4" }]];
const IconSpy = createReactComponent("outline", "spy", "Spy", __iconNode);

export { __iconNode, IconSpy as default };
//# sourceMappingURL=IconSpy.mjs.map
