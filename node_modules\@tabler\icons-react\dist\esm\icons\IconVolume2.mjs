/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M15 8a5 5 0 0 1 0 8", "key": "svg-0" }], ["path", { "d": "M6 15h-2a1 1 0 0 1 -1 -1v-4a1 1 0 0 1 1 -1h2l3.5 -4.5a.8 .8 0 0 1 1.5 .5v14a.8 .8 0 0 1 -1.5 .5l-3.5 -4.5", "key": "svg-1" }]];
const IconVolume2 = createReactComponent("outline", "volume-2", "Volume2", __iconNode);

export { __iconNode, IconVolume2 as default };
//# sourceMappingURL=IconVolume2.mjs.map
