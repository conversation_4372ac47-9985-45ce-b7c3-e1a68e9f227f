/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M6 21l15 -15l-3 -3l-15 15l3 3", "key": "svg-0" }], ["path", { "d": "M15 6l3 3", "key": "svg-1" }], ["path", { "d": "M9 3a2 2 0 0 0 2 2a2 2 0 0 0 -2 2a2 2 0 0 0 -2 -2a2 2 0 0 0 2 -2", "key": "svg-2" }], ["path", { "d": "M19 13a2 2 0 0 0 2 2a2 2 0 0 0 -2 2a2 2 0 0 0 -2 -2a2 2 0 0 0 2 -2", "key": "svg-3" }]];
const IconWand = createReactComponent("outline", "wand", "Wand", __iconNode);

export { __iconNode, IconWand as default };
//# sourceMappingURL=IconWand.mjs.map
