/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 21a9 9 0 0 0 9 -9a9 9 0 0 0 -9 -9a9 9 0 0 0 -9 9a9 9 0 0 0 9 9z", "key": "svg-0" }], ["path", { "d": "M15 16l-3 -8l-3 8", "key": "svg-1" }], ["path", { "d": "M14 14h-4", "key": "svg-2" }]];
const IconXboxA = createReactComponent("outline", "xbox-a", "XboxA", __iconNode);

export { __iconNode, IconXboxA as default };
//# sourceMappingURL=IconXboxA.mjs.map
