/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 4c5.333 1.333 10.667 1.333 16 0", "key": "svg-0" }], ["path", { "d": "M4 8h16", "key": "svg-1" }], ["path", { "d": "M12 5v3", "key": "svg-2" }], ["path", { "d": "M18 4.5v15.5", "key": "svg-3" }], ["path", { "d": "M6 4.5v15.5", "key": "svg-4" }]];
const IconTorii = createReactComponent("outline", "torii", "Torii", __iconNode);

export { __iconNode, IconTorii as default };
//# sourceMappingURL=IconTorii.mjs.map
