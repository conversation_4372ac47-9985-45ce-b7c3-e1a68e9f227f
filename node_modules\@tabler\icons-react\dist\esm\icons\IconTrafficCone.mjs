/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 20l16 0", "key": "svg-0" }], ["path", { "d": "M9.4 10l5.2 0", "key": "svg-1" }], ["path", { "d": "M7.8 15l8.4 0", "key": "svg-2" }], ["path", { "d": "M6 20l5 -15h2l5 15", "key": "svg-3" }]];
const IconTrafficCone = createReactComponent("outline", "traffic-cone", "TrafficCone", __iconNode);

export { __iconNode, IconTrafficCone as default };
//# sourceMappingURL=IconTrafficCone.mjs.map
