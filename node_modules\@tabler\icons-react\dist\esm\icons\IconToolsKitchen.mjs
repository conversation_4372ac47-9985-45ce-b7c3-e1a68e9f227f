/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 3h8l-1 9h-6z", "key": "svg-0" }], ["path", { "d": "M7 18h2v3h-2z", "key": "svg-1" }], ["path", { "d": "M20 3v12h-5c-.023 -3.681 .184 -7.406 5 -12z", "key": "svg-2" }], ["path", { "d": "M20 15v6h-1v-3", "key": "svg-3" }], ["path", { "d": "M8 12l0 6", "key": "svg-4" }]];
const IconToolsKitchen = createReactComponent("outline", "tools-kitchen", "ToolsKitchen", __iconNode);

export { __iconNode, IconToolsKitchen as default };
//# sourceMappingURL=IconToolsKitchen.mjs.map
