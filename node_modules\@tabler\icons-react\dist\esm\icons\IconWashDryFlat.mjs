/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 6a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3v-12z", "key": "svg-0" }], ["path", { "d": "M7 12h10", "key": "svg-1" }]];
const IconWashDryFlat = createReactComponent("outline", "wash-dry-flat", "WashDryFlat", __iconNode);

export { __iconNode, IconWashDryFlat as default };
//# sourceMappingURL=IconWashDryFlat.mjs.map
