/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 21h-7a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v8", "key": "svg-0" }], ["path", { "d": "M3 10h18", "key": "svg-1" }], ["path", { "d": "M10 3v18", "key": "svg-2" }], ["path", { "d": "M16 22l5 -5", "key": "svg-3" }], ["path", { "d": "M21 21.5v-4.5h-4.5", "key": "svg-4" }]];
const IconTableShare = createReactComponent("outline", "table-share", "TableShare", __iconNode);

export { __iconNode, IconTableShare as default };
//# sourceMappingURL=IconTableShare.mjs.map
