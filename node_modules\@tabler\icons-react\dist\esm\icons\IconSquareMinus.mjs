/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12.5 21h-7.5a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v10", "key": "svg-0" }], ["path", { "d": "M16 19h6", "key": "svg-1" }]];
const IconSquareMinus = createReactComponent("outline", "square-minus", "SquareMinus", __iconNode);

export { __iconNode, IconSquareMinus as default };
//# sourceMappingURL=IconSquareMinus.mjs.map
