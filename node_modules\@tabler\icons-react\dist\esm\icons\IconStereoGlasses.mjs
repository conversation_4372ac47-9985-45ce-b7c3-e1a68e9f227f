/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M8 3h-2l-3 9", "key": "svg-0" }], ["path", { "d": "M16 3h2l3 9", "key": "svg-1" }], ["path", { "d": "M3 12v7a1 1 0 0 0 1 1h4.586a1 1 0 0 0 .707 -.293l2 -2a1 1 0 0 1 1.414 0l2 2a1 1 0 0 0 .707 .293h4.586a1 1 0 0 0 1 -1v-7h-18z", "key": "svg-2" }], ["path", { "d": "M7 16h1", "key": "svg-3" }], ["path", { "d": "M16 16h1", "key": "svg-4" }]];
const IconStereoGlasses = createReactComponent("outline", "stereo-glasses", "StereoGlasses", __iconNode);

export { __iconNode, IconStereoGlasses as default };
//# sourceMappingURL=IconStereoGlasses.mjs.map
