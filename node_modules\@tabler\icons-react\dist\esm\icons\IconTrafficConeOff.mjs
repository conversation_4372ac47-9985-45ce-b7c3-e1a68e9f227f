/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 20h16", "key": "svg-0" }], ["path", { "d": "M9.4 10h.6m4 0h.6", "key": "svg-1" }], ["path", { "d": "M7.8 15h7.2", "key": "svg-2" }], ["path", { "d": "M6 20l3.5 -10.5", "key": "svg-3" }], ["path", { "d": "M10.5 6.5l.5 -1.5h2l2 6m2 6l1 3", "key": "svg-4" }], ["path", { "d": "M3 3l18 18", "key": "svg-5" }]];
const IconTrafficConeOff = createReactComponent("outline", "traffic-cone-off", "TrafficConeOff", __iconNode);

export { __iconNode, IconTrafficConeOff as default };
//# sourceMappingURL=IconTrafficConeOff.mjs.map
