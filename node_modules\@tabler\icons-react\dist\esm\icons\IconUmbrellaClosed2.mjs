/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M6.697 12.071l11.313 -7.071l-7.07 11.314z", "key": "svg-0" }], ["path", { "d": "M8.743 14.475l-2.121 2.121c-1.886 1.886 .943 4.715 2.828 2.829", "key": "svg-1" }]];
const IconUmbrellaClosed2 = createReactComponent("outline", "umbrella-closed-2", "UmbrellaClosed2", __iconNode);

export { __iconNode, IconUmbrellaClosed2 as default };
//# sourceMappingURL=IconUmbrellaClosed2.mjs.map
