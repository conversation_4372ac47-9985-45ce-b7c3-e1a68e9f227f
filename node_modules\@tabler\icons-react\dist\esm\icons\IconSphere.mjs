/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 12c0 1.657 4.03 3 9 3s9 -1.343 9 -3", "key": "svg-0" }], ["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-1" }]];
const IconSphere = createReactComponent("outline", "sphere", "Sphere", __iconNode);

export { __iconNode, IconSphere as default };
//# sourceMappingURL=IconSphere.mjs.map
