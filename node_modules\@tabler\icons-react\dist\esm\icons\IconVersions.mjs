/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M10 5m0 2a2 2 0 0 1 2 -2h6a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-6a2 2 0 0 1 -2 -2z", "key": "svg-0" }], ["path", { "d": "M7 7l0 10", "key": "svg-1" }], ["path", { "d": "M4 8l0 8", "key": "svg-2" }]];
const IconVersions = createReactComponent("outline", "versions", "Versions", __iconNode);

export { __iconNode, IconVersions as default };
//# sourceMappingURL=IconVersions.mjs.map
