/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 12a4 4 0 1 1 8 0a4 4 0 0 1 -8 0z", "key": "svg-0" }], ["path", { "d": "M12 12h8", "key": "svg-1" }], ["path", { "d": "M17 15l3 -3l-3 -3", "key": "svg-2" }]];
const IconSwipeRight = createReactComponent("outline", "swipe-right", "SwipeRight", __iconNode);

export { __iconNode, IconSwipeRight as default };
//# sourceMappingURL=IconSwipeRight.mjs.map
