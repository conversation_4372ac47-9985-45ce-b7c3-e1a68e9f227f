/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 7v-2h13v2", "key": "svg-0" }], ["path", { "d": "M10 5v14", "key": "svg-1" }], ["path", { "d": "M12 19h-4", "key": "svg-2" }], ["path", { "d": "M15 13v-1h6v1", "key": "svg-3" }], ["path", { "d": "M18 12v7", "key": "svg-4" }], ["path", { "d": "M17 19h2", "key": "svg-5" }]];
const IconTextSize = createReactComponent("outline", "text-size", "TextSize", __iconNode);

export { __iconNode, IconTextSize as default };
//# sourceMappingURL=IconTextSize.mjs.map
