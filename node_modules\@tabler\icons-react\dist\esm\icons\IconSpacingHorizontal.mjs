/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M20 20h-2a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h2", "key": "svg-0" }], ["path", { "d": "M4 20h2a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2", "key": "svg-1" }], ["path", { "d": "M12 8v8", "key": "svg-2" }]];
const IconSpacingHorizontal = createReactComponent("outline", "spacing-horizontal", "SpacingHorizontal", __iconNode);

export { __iconNode, IconSpacingHorizontal as default };
//# sourceMappingURL=IconSpacingHorizontal.mjs.map
