/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M5 5m0 .5a.5 .5 0 0 1 .5 -.5h4a.5 .5 0 0 1 .5 .5v4a.5 .5 0 0 1 -.5 .5h-4a.5 .5 0 0 1 -.5 -.5z", "key": "svg-0" }], ["path", { "d": "M5 14m0 .5a.5 .5 0 0 1 .5 -.5h4a.5 .5 0 0 1 .5 .5v4a.5 .5 0 0 1 -.5 .5h-4a.5 .5 0 0 1 -.5 -.5z", "key": "svg-1" }], ["path", { "d": "M14 15l3 3l3 -3", "key": "svg-2" }], ["path", { "d": "M17 18v-12", "key": "svg-3" }]];
const IconSortDescending2 = createReactComponent("outline", "sort-descending-2", "SortDescending2", __iconNode);

export { __iconNode, IconSortDescending2 as default };
//# sourceMappingURL=IconSortDescending2.mjs.map
