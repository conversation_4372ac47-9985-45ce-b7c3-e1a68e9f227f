/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M20 12a4 4 0 1 0 -8 0a4 4 0 0 0 8 0z", "key": "svg-0" }], ["path", { "d": "M12 12h-8", "key": "svg-1" }], ["path", { "d": "M7 15l-3 -3l3 -3", "key": "svg-2" }]];
const IconSwipeLeft = createReactComponent("outline", "swipe-left", "SwipeLeft", __iconNode);

export { __iconNode, IconSwipeLeft as default };
//# sourceMappingURL=IconSwipeLeft.mjs.map
