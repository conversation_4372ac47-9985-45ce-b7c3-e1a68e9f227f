/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M17 3.34a10 10 0 1 1 -14.995 8.984l-.005 -.324l.005 -.324a10 10 0 0 1 14.995 -8.336zm-9 1.732a8 8 0 0 0 4 14.928l.2 -.005a4 4 0 0 0 0 -7.99l-.2 -.005a4 4 0 0 1 -.2 -7.995l.2 -.005a7.995 7.995 0 0 0 -4 1.072zm4 1.428a1.5 1.5 0 1 0 0 3a1.5 1.5 0 0 0 0 -3z", "key": "svg-0" }], ["path", { "d": "M12 14.5a1.5 1.5 0 1 1 0 3a1.5 1.5 0 0 1 0 -3z", "key": "svg-1" }]];
const IconYinYangFilled = createReactComponent("filled", "yin-yang-filled", "YinYangFilled", __iconNode);

export { __iconNode, IconYinYangFilled as default };
//# sourceMappingURL=IconYinYangFilled.mjs.map
