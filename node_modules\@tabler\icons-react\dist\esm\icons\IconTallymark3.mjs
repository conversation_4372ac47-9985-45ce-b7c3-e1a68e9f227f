/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M8 5l0 14", "key": "svg-0" }], ["path", { "d": "M12 5l0 14", "key": "svg-1" }], ["path", { "d": "M16 5l0 14", "key": "svg-2" }]];
const IconTallymark3 = createReactComponent("outline", "tallymark-3", "Tallymark3", __iconNode);

export { __iconNode, IconTallymark3 as default };
//# sourceMappingURL=IconTallymark3.mjs.map
