/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M16 15l-3 -3l3 -3", "key": "svg-0" }], ["path", { "d": "M11 15l-3 -3l3 -3", "key": "svg-1" }], ["path", { "d": "M3 5a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-14z", "key": "svg-2" }]];
const IconSquareChevronsLeft = createReactComponent("outline", "square-chevrons-left", "SquareChevronsLeft", __iconNode);

export { __iconNode, IconSquareChevronsLeft as default };
//# sourceMappingURL=IconSquareChevronsLeft.mjs.map
