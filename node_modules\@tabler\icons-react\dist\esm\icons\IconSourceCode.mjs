/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M14.5 4h2.5a3 3 0 0 1 3 3v10a3 3 0 0 1 -3 3h-10a3 3 0 0 1 -3 -3v-5", "key": "svg-0" }], ["path", { "d": "M6 5l-2 2l2 2", "key": "svg-1" }], ["path", { "d": "M10 9l2 -2l-2 -2", "key": "svg-2" }]];
const IconSourceCode = createReactComponent("outline", "source-code", "SourceCode", __iconNode);

export { __iconNode, IconSourceCode as default };
//# sourceMappingURL=IconSourceCode.mjs.map
