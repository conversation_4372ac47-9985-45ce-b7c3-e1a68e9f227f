/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M5 8h3m4 0h1.5a2.5 2.5 0 1 0 -2.34 -3.24", "key": "svg-0" }], ["path", { "d": "M3 12h9", "key": "svg-1" }], ["path", { "d": "M16 12h2.5a2.5 2.5 0 0 1 1.801 4.282", "key": "svg-2" }], ["path", { "d": "M4 16h5.5a2.5 2.5 0 1 1 -2.34 3.24", "key": "svg-3" }], ["path", { "d": "M3 3l18 18", "key": "svg-4" }]];
const IconWindOff = createReactComponent("outline", "wind-off", "WindOff", __iconNode);

export { __iconNode, IconWindOff as default };
//# sourceMappingURL=IconWindOff.mjs.map
