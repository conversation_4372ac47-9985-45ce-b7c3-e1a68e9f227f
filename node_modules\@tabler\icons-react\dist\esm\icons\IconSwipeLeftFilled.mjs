/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M16 7a5 5 0 1 1 -4.9 6.001l-4.685 -.001l1.292 1.293a1 1 0 0 1 .083 1.32l-.083 .094a1 1 0 0 1 -1.414 0l-3 -3a1 1 0 0 1 -.097 -.112l-.071 -.11l-.054 -.114l-.035 -.105l-.025 -.118l-.007 -.058l-.004 -.09l.003 -.075l.017 -.126l.03 -.111l.044 -.111l.052 -.098l.067 -.096l.08 -.09l3 -3a1 1 0 0 1 1.414 1.414l-1.292 1.293h4.685a5 5 0 0 1 4.9 -4", "key": "svg-0" }]];
const IconSwipeLeftFilled = createReactComponent("filled", "swipe-left-filled", "SwipeLeftFilled", __iconNode);

export { __iconNode, IconSwipeLeftFilled as default };
//# sourceMappingURL=IconSwipeLeftFilled.mjs.map
