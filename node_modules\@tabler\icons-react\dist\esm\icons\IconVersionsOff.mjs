/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M10.184 6.162a2 2 0 0 1 1.816 -1.162h6a2 2 0 0 1 2 2v9m-1.185 2.827a1.993 1.993 0 0 1 -.815 .173h-6a2 2 0 0 1 -2 -2v-7", "key": "svg-0" }], ["path", { "d": "M7 7v10", "key": "svg-1" }], ["path", { "d": "M4 8v8", "key": "svg-2" }], ["path", { "d": "M3 3l18 18", "key": "svg-3" }]];
const IconVersionsOff = createReactComponent("outline", "versions-off", "VersionsOff", __iconNode);

export { __iconNode, IconVersionsOff as default };
//# sourceMappingURL=IconVersionsOff.mjs.map
