/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M17 20v-11.5a4.5 4.5 0 1 0 -9 0v8.5", "key": "svg-0" }], ["path", { "d": "M11 14l-3 3l-3 -3", "key": "svg-1" }]];
const IconUTurnLeft = createReactComponent("outline", "u-turn-left", "UTurnLeft", __iconNode);

export { __iconNode, IconUTurnLeft as default };
//# sourceMappingURL=IconUTurnLeft.mjs.map
