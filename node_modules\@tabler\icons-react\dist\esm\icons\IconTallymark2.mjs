/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M10 5l0 14", "key": "svg-0" }], ["path", { "d": "M14 5l0 14", "key": "svg-1" }]];
const IconTallymark2 = createReactComponent("outline", "tallymark-2", "Tallymark2", __iconNode);

export { __iconNode, IconTallymark2 as default };
//# sourceMappingURL=IconTallymark2.mjs.map
