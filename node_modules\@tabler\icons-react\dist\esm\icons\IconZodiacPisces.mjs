/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M5 3a21 21 0 0 1 0 18", "key": "svg-0" }], ["path", { "d": "M19 3a21 21 0 0 0 0 18", "key": "svg-1" }], ["path", { "d": "M5 12l14 0", "key": "svg-2" }]];
const IconZodiacPisces = createReactComponent("outline", "zodiac-pisces", "ZodiacPisces", __iconNode);

export { __iconNode, IconZodiacPisces as default };
//# sourceMappingURL=IconZodiacPisces.mjs.map
