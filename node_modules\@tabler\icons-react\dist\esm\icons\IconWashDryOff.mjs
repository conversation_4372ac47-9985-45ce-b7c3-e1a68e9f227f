/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M20.116 20.127a2.99 2.99 0 0 1 -2.116 .873h-12a3 3 0 0 1 -3 -3v-12c0 -.827 .335 -1.576 .877 -2.12m3.123 -.88h11a3 3 0 0 1 3 3v11", "key": "svg-0" }], ["path", { "d": "M3 3l18 18", "key": "svg-1" }]];
const IconWashDryOff = createReactComponent("outline", "wash-dry-off", "WashDryOff", __iconNode);

export { __iconNode, IconWashDryOff as default };
//# sourceMappingURL=IconWashDryOff.mjs.map
