/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M20.48 15.016l-6.843 -11.426a1.914 1.914 0 0 0 -3.274 0l-8.106 13.535a1.914 1.914 0 0 0 1.636 2.871h8.107", "key": "svg-0" }], ["path", { "d": "M16 19h6", "key": "svg-1" }]];
const IconTriangleMinus2 = createReactComponent("outline", "triangle-minus-2", "TriangleMinus2", __iconNode);

export { __iconNode, IconTriangleMinus2 as default };
//# sourceMappingURL=IconTriangleMinus2.mjs.map
