/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M16 3l4 4l-4 4", "key": "svg-0" }], ["path", { "d": "M10 7l10 0", "key": "svg-1" }], ["path", { "d": "M8 13l-4 4l4 4", "key": "svg-2" }], ["path", { "d": "M4 17l9 0", "key": "svg-3" }]];
const IconSwitchHorizontal = createReactComponent("outline", "switch-horizontal", "SwitchHorizontal", __iconNode);

export { __iconNode, IconSwitchHorizontal as default };
//# sourceMappingURL=IconSwitchHorizontal.mjs.map
