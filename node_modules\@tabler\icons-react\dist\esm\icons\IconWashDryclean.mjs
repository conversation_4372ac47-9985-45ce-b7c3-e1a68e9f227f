/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }]];
const IconWashDryclean = createReactComponent("outline", "wash-dryclean", "WashDryclean", __iconNode);

export { __iconNode, IconWashDryclean as default };
//# sourceMappingURL=IconWashDryclean.mjs.map
