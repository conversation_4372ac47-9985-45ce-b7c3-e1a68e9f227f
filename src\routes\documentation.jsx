import { createFileRoute } from "@tanstack/react-router";

export const Route = createFileRoute("/documentation")({
  component: DocumentationComponent,
});

function DocumentationComponent() {
  const sections = [
    {
      title: "Getting Started",
      description: "Learn the basics and set up your environment",
      items: ["Installation", "Quick Start", "Configuration"],
    },
    {
      title: "API Reference",
      description: "Complete API documentation and examples",
      items: ["Authentication", "Endpoints", "Rate Limits"],
    },
    {
      title: "Guides",
      description: "Step-by-step tutorials and best practices",
      items: ["Best Practices", "Common Patterns", "Troubleshooting"],
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Documentation</h1>
        <p className="text-muted-foreground">
          Comprehensive guides and API reference for developers.
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {sections.map((section, index) => (
          <div key={index} className="rounded-lg border p-6">
            <h3 className="text-lg font-semibold mb-2">{section.title}</h3>
            <p className="text-sm text-muted-foreground mb-4">{section.description}</p>
            <ul className="space-y-2">
              {section.items.map((item, itemIndex) => (
                <li key={itemIndex}>
                  <a 
                    href="#" 
                    className="text-sm text-primary hover:underline"
                  >
                    {item}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
      
      <div className="rounded-lg border p-6">
        <h3 className="text-lg font-semibold mb-4">Search Documentation</h3>
        <div className="flex gap-2">
          <input 
            type="text" 
            placeholder="Search for topics, guides, or API methods..."
            className="flex-1 rounded-md border border-input bg-background px-3 py-2 text-sm"
          />
          <button className="rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90">
            Search
          </button>
        </div>
      </div>
    </div>
  );
}
