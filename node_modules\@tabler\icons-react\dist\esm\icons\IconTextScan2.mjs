/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 8v-2a2 2 0 0 1 2 -2h2", "key": "svg-0" }], ["path", { "d": "M4 16v2a2 2 0 0 0 2 2h2", "key": "svg-1" }], ["path", { "d": "M16 4h2a2 2 0 0 1 2 2v2", "key": "svg-2" }], ["path", { "d": "M16 20h2a2 2 0 0 0 2 -2v-2", "key": "svg-3" }], ["path", { "d": "M8 12h8", "key": "svg-4" }], ["path", { "d": "M8 9h6", "key": "svg-5" }], ["path", { "d": "M8 15h4", "key": "svg-6" }]];
const IconTextScan2 = createReactComponent("outline", "text-scan-2", "TextScan2", __iconNode);

export { __iconNode, IconTextScan2 as default };
//# sourceMappingURL=IconTextScan2.mjs.map
