/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M7 20v-11.5a4.5 4.5 0 0 1 9 0v8.5", "key": "svg-0" }], ["path", { "d": "M13 14l3 3l3 -3", "key": "svg-1" }]];
const IconUTurnRight = createReactComponent("outline", "u-turn-right", "UTurnRight", __iconNode);

export { __iconNode, IconUTurnRight as default };
//# sourceMappingURL=IconUTurnRight.mjs.map
