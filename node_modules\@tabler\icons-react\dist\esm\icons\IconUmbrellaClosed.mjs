/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M9 16l3 -13l3 13z", "key": "svg-0" }], ["path", { "d": "M12 16v3c0 2.667 4 2.667 4 0", "key": "svg-1" }]];
const IconUmbrellaClosed = createReactComponent("outline", "umbrella-closed", "UmbrellaClosed", __iconNode);

export { __iconNode, IconUmbrellaClosed as default };
//# sourceMappingURL=IconUmbrellaClosed.mjs.map
