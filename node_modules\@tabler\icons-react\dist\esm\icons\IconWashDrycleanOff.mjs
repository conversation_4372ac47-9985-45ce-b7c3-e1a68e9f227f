/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M20.048 16.033a9 9 0 0 0 -12.094 -12.075m-2.321 1.682a9 9 0 0 0 12.733 12.723", "key": "svg-0" }], ["path", { "d": "M3 3l18 18", "key": "svg-1" }]];
const IconWashDrycleanOff = createReactComponent("outline", "wash-dryclean-off", "WashDrycleanOff", __iconNode);

export { __iconNode, IconWashDrycleanOff as default };
//# sourceMappingURL=IconWashDrycleanOff.mjs.map
