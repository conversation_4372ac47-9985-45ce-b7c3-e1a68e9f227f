/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 7l6 6l4 -4l8 8", "key": "svg-0" }], ["path", { "d": "M21 10l0 7l-7 0", "key": "svg-1" }]];
const IconTrendingDown = createReactComponent("outline", "trending-down", "TrendingDown", __iconNode);

export { __iconNode, IconTrendingDown as default };
//# sourceMappingURL=IconTrendingDown.mjs.map
