/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M19 3v12h-5c-.023 -3.681 .184 -7.406 5 -12zm0 12v6h-1v-3m-10 -14v17m-3 -17v3a3 3 0 1 0 6 0v-3", "key": "svg-0" }]];
const IconToolsKitchen2 = createReactComponent("outline", "tools-kitchen-2", "ToolsKitchen2", __iconNode);

export { __iconNode, IconToolsKitchen2 as default };
//# sourceMappingURL=IconToolsKitchen2.mjs.map
