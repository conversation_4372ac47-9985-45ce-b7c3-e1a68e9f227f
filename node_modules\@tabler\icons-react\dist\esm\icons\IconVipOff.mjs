/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 5h2m4 0h12", "key": "svg-0" }], ["path", { "d": "M3 19h16", "key": "svg-1" }], ["path", { "d": "M4 9l2 6h1l2 -6", "key": "svg-2" }], ["path", { "d": "M12 12v3", "key": "svg-3" }], ["path", { "d": "M16 12v-3h2a2 2 0 1 1 0 4h-1", "key": "svg-4" }], ["path", { "d": "M3 3l18 18", "key": "svg-5" }]];
const IconVipOff = createReactComponent("outline", "vip-off", "VipOff", __iconNode);

export { __iconNode, IconVipOff as default };
//# sourceMappingURL=IconVipOff.mjs.map
