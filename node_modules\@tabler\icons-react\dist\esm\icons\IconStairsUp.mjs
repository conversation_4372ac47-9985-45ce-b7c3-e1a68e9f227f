/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M22 6h-5v5h-5v5h-5v5h-5", "key": "svg-0" }], ["path", { "d": "M6 10v-7", "key": "svg-1" }], ["path", { "d": "M3 6l3 -3l3 3", "key": "svg-2" }]];
const IconStairsUp = createReactComponent("outline", "stairs-up", "StairsUp", __iconNode);

export { __iconNode, IconStairsUp as default };
//# sourceMappingURL=IconStairsUp.mjs.map
