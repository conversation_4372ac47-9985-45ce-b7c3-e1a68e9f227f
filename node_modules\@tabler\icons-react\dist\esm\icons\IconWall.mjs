/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 4m0 2a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2z", "key": "svg-0" }], ["path", { "d": "M4 8h16", "key": "svg-1" }], ["path", { "d": "M20 12h-16", "key": "svg-2" }], ["path", { "d": "M4 16h16", "key": "svg-3" }], ["path", { "d": "M9 4v4", "key": "svg-4" }], ["path", { "d": "M14 8v4", "key": "svg-5" }], ["path", { "d": "M8 12v4", "key": "svg-6" }], ["path", { "d": "M16 12v4", "key": "svg-7" }], ["path", { "d": "M11 16v4", "key": "svg-8" }]];
const IconWall = createReactComponent("outline", "wall", "Wall", __iconNode);

export { __iconNode, IconWall as default };
//# sourceMappingURL=IconWall.mjs.map
