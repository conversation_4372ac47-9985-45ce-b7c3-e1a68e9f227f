/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M7 17m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-0" }], ["path", { "d": "M17 17m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-1" }], ["path", { "d": "M5 17h-2v-11a1 1 0 0 1 1 -1h9v6h-5l2 2m0 -4l-2 2", "key": "svg-2" }], ["path", { "d": "M9 17l6 0", "key": "svg-3" }], ["path", { "d": "M13 6h5l3 5v6h-2", "key": "svg-4" }]];
const IconTruckReturn = createReactComponent("outline", "truck-return", "TruckReturn", __iconNode);

export { __iconNode, IconTruckReturn as default };
//# sourceMappingURL=IconTruckReturn.mjs.map
