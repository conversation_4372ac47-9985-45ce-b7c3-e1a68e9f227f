/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M18 4h-6a3 3 0 0 0 -3 3v10a3 3 0 0 0 3 3h6a3 3 0 0 0 3 -3v-10a3 3 0 0 0 -3 -3z", "key": "svg-0" }], ["path", { "d": "M7 6a1 1 0 0 1 .993 .883l.007 .117v10a1 1 0 0 1 -1.993 .117l-.007 -.117v-10a1 1 0 0 1 1 -1z", "key": "svg-1" }], ["path", { "d": "M4 7a1 1 0 0 1 .993 .883l.007 .117v8a1 1 0 0 1 -1.993 .117l-.007 -.117v-8a1 1 0 0 1 1 -1z", "key": "svg-2" }]];
const IconVersionsFilled = createReactComponent("filled", "versions-filled", "VersionsFilled", __iconNode);

export { __iconNode, IconVersionsFilled as default };
//# sourceMappingURL=IconVersionsFilled.mjs.map
