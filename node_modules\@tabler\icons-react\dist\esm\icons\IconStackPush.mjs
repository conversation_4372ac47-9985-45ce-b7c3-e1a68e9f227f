/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M6 10l-2 1l8 4l8 -4l-2 -1", "key": "svg-0" }], ["path", { "d": "M4 15l8 4l8 -4", "key": "svg-1" }], ["path", { "d": "M12 4v7", "key": "svg-2" }], ["path", { "d": "M15 8l-3 3l-3 -3", "key": "svg-3" }]];
const IconStackPush = createReactComponent("outline", "stack-push", "StackPush", __iconNode);

export { __iconNode, IconStackPush as default };
//# sourceMappingURL=IconStackPush.mjs.map
