/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 15l3 3l3 -3", "key": "svg-0" }], ["path", { "d": "M7 6v12", "key": "svg-1" }], ["path", { "d": "M14 15a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1v-4z", "key": "svg-2" }], ["path", { "d": "M17 4l-3.5 6h7z", "key": "svg-3" }]];
const IconSortDescendingShapes = createReactComponent("outline", "sort-descending-shapes", "SortDescendingShapes", __iconNode);

export { __iconNode, IconSortDescendingShapes as default };
//# sourceMappingURL=IconSortDescendingShapes.mjs.map
