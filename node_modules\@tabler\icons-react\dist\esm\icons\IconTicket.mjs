/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M15 5l0 2", "key": "svg-0" }], ["path", { "d": "M15 11l0 2", "key": "svg-1" }], ["path", { "d": "M15 17l0 2", "key": "svg-2" }], ["path", { "d": "M5 5h14a2 2 0 0 1 2 2v3a2 2 0 0 0 0 4v3a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-3a2 2 0 0 0 0 -4v-3a2 2 0 0 1 2 -2", "key": "svg-3" }]];
const IconTicket = createReactComponent("outline", "ticket", "Ticket", __iconNode);

export { __iconNode, IconTicket as default };
//# sourceMappingURL=IconTicket.mjs.map
