/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 18l.01 0", "key": "svg-0" }], ["path", { "d": "M9.172 15.172a4 4 0 0 1 5.656 0", "key": "svg-1" }], ["path", { "d": "M6.343 12.343a8 8 0 0 1 11.314 0", "key": "svg-2" }]];
const IconWifi2 = createReactComponent("outline", "wifi-2", "Wifi2", __iconNode);

export { __iconNode, IconWifi2 as default };
//# sourceMappingURL=IconWifi2.mjs.map
