/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M15 5v2", "key": "svg-0" }], ["path", { "d": "M15 17v2", "key": "svg-1" }], ["path", { "d": "M9 5h10a2 2 0 0 1 2 2v3a2 2 0 1 0 0 4v3m-2 2h-14a2 2 0 0 1 -2 -2v-3a2 2 0 1 0 0 -4v-3a2 2 0 0 1 2 -2", "key": "svg-2" }], ["path", { "d": "M3 3l18 18", "key": "svg-3" }]];
const IconTicketOff = createReactComponent("outline", "ticket-off", "TicketOff", __iconNode);

export { __iconNode, IconTicketOff as default };
//# sourceMappingURL=IconTicketOff.mjs.map
