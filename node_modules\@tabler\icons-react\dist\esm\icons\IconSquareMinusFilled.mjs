/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M19 2a3 3 0 0 1 3 3v14a3 3 0 0 1 -3 3h-14a3 3 0 0 1 -3 -3v-14a3 3 0 0 1 3 -3zm-4 9h-6l-.117 .007a1 1 0 0 0 .117 1.993h6l.117 -.007a1 1 0 0 0 -.117 -1.993z", "key": "svg-0" }]];
const IconSquareMinusFilled = createReactComponent("filled", "square-minus-filled", "SquareMinusFilled", __iconNode);

export { __iconNode, IconSquareMinusFilled as default };
//# sourceMappingURL=IconSquareMinusFilled.mjs.map
