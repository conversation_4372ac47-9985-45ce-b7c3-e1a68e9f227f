/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M20.946 12.99a9 9 0 1 0 -9.46 7.995", "key": "svg-0" }], ["path", { "d": "M3.6 9h16.8", "key": "svg-1" }], ["path", { "d": "M3.6 15h13.9", "key": "svg-2" }], ["path", { "d": "M11.5 3a17 17 0 0 0 0 18", "key": "svg-3" }], ["path", { "d": "M12.5 3a16.997 16.997 0 0 1 2.311 12.001", "key": "svg-4" }], ["path", { "d": "M15 19l2 2l4 -4", "key": "svg-5" }]];
const IconWorldCheck = createReactComponent("outline", "world-check", "WorldCheck", __iconNode);

export { __iconNode, IconWorldCheck as default };
//# sourceMappingURL=IconWorldCheck.mjs.map
