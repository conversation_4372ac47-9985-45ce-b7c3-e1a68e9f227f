/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 20m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-0" }], ["path", { "d": "M10 20h-6", "key": "svg-1" }], ["path", { "d": "M14 20h6", "key": "svg-2" }], ["path", { "d": "M12 15l-2 -2h-3a1 1 0 0 1 -1 -1v-8a1 1 0 0 1 1 -1h10a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-3l-2 2z", "key": "svg-3" }], ["path", { "d": "M10 8h4", "key": "svg-4" }]];
const IconTimelineEventMinus = createReactComponent("outline", "timeline-event-minus", "TimelineEventMinus", __iconNode);

export { __iconNode, IconTimelineEventMinus as default };
//# sourceMappingURL=IconTimelineEventMinus.mjs.map
