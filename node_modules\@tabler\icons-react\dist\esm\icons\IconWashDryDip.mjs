/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 3m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z", "key": "svg-0" }], ["path", { "d": "M12 7v10", "key": "svg-1" }], ["path", { "d": "M16 7v10", "key": "svg-2" }], ["path", { "d": "M8 7v10", "key": "svg-3" }]];
const IconWashDryDip = createReactComponent("outline", "wash-dry-dip", "WashDryDip", __iconNode);

export { __iconNode, IconWashDryDip as default };
//# sourceMappingURL=IconWashDryDip.mjs.map
