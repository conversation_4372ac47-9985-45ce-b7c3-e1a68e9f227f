/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M14 6a2 2 0 1 0 -4 0a2 2 0 0 0 4 0z", "key": "svg-0" }], ["path", { "d": "M7 18a2 2 0 1 0 -4 0a2 2 0 0 0 4 0z", "key": "svg-1" }], ["path", { "d": "M21 18a2 2 0 1 0 -4 0a2 2 0 0 0 4 0z", "key": "svg-2" }], ["path", { "d": "M7 18h10", "key": "svg-3" }], ["path", { "d": "M18 16l-5 -8", "key": "svg-4" }], ["path", { "d": "M11 8l-5 8", "key": "svg-5" }]];
const IconTopologyRing2 = createReactComponent("outline", "topology-ring-2", "TopologyRing2", __iconNode);

export { __iconNode, IconTopologyRing2 as default };
//# sourceMappingURL=IconTopologyRing2.mjs.map
