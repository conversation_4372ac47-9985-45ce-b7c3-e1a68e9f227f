/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M20 18a2 2 0 1 0 -4 0a2 2 0 0 0 4 0z", "key": "svg-0" }], ["path", { "d": "M8 18a2 2 0 1 0 -4 0a2 2 0 0 0 4 0z", "key": "svg-1" }], ["path", { "d": "M8 6a2 2 0 1 0 -4 0a2 2 0 0 0 4 0z", "key": "svg-2" }], ["path", { "d": "M20 6a2 2 0 1 0 -4 0a2 2 0 0 0 4 0z", "key": "svg-3" }], ["path", { "d": "M14 12a2 2 0 1 0 -4 0a2 2 0 0 0 4 0z", "key": "svg-4" }], ["path", { "d": "M7.5 7.5l3 3", "key": "svg-5" }], ["path", { "d": "M6 8v8", "key": "svg-6" }], ["path", { "d": "M18 16v-8", "key": "svg-7" }], ["path", { "d": "M8 6h8", "key": "svg-8" }], ["path", { "d": "M16 18h-8", "key": "svg-9" }]];
const IconTopologyComplex = createReactComponent("outline", "topology-complex", "TopologyComplex", __iconNode);

export { __iconNode, IconTopologyComplex as default };
//# sourceMappingURL=IconTopologyComplex.mjs.map
