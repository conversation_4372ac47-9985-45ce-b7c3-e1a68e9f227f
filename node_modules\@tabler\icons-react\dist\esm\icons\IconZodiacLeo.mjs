/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M13 17a4 4 0 1 0 8 0", "key": "svg-0" }], ["path", { "d": "M6 16m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-1" }], ["path", { "d": "M11 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0", "key": "svg-2" }], ["path", { "d": "M7 7c0 3 2 5 2 9", "key": "svg-3" }], ["path", { "d": "M15 7c0 4 -2 6 -2 10", "key": "svg-4" }]];
const IconZodiacLeo = createReactComponent("outline", "zodiac-leo", "ZodiacLeo", __iconNode);

export { __iconNode, IconZodiacLeo as default };
//# sourceMappingURL=IconZodiacLeo.mjs.map
