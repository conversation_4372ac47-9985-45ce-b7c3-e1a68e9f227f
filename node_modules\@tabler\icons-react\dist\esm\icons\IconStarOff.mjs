/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 3l18 18", "key": "svg-0" }], ["path", { "d": "M10.012 6.016l1.981 -4.014l3.086 6.253l6.9 1l-4.421 4.304m.012 4.01l.588 3.426l-6.158 -3.245l-6.172 3.245l1.179 -6.873l-5 -4.867l6.327 -.917", "key": "svg-1" }]];
const IconStarOff = createReactComponent("outline", "star-off", "StarOff", __iconNode);

export { __iconNode, IconStarOff as default };
//# sourceMappingURL=IconStarOff.mjs.map
