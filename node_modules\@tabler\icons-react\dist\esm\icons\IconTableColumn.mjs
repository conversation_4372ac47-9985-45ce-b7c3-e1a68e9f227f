/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 5a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-14z", "key": "svg-0" }], ["path", { "d": "M10 10h11", "key": "svg-1" }], ["path", { "d": "M10 3v18", "key": "svg-2" }], ["path", { "d": "M9 3l-6 6", "key": "svg-3" }], ["path", { "d": "M10 7l-7 7", "key": "svg-4" }], ["path", { "d": "M10 12l-7 7", "key": "svg-5" }], ["path", { "d": "M10 17l-4 4", "key": "svg-6" }]];
const IconTableColumn = createReactComponent("outline", "table-column", "TableColumn", __iconNode);

export { __iconNode, IconTableColumn as default };
//# sourceMappingURL=IconTableColumn.mjs.map
