/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M8 2a4 4 0 1 0 8 0", "key": "svg-0" }], ["path", { "d": "M4 3h1", "key": "svg-1" }], ["path", { "d": "M19 3h1", "key": "svg-2" }], ["path", { "d": "M12 9v1", "key": "svg-3" }], ["path", { "d": "M17.2 7.2l.707 .707", "key": "svg-4" }], ["path", { "d": "M6.8 7.2l-.7 .7", "key": "svg-5" }], ["path", { "d": "M4.28 21h15.44a1 1 0 0 0 .97 -1.243l-1.5 -6a1 1 0 0 0 -.97 -.757h-12.44a1 1 0 0 0 -.97 .757l-1.5 6a1 1 0 0 0 .97 1.243z", "key": "svg-6" }], ["path", { "d": "M4 17h16", "key": "svg-7" }], ["path", { "d": "M10 13l-1 8", "key": "svg-8" }], ["path", { "d": "M14 13l1 8", "key": "svg-9" }]];
const IconSolarPanel2 = createReactComponent("outline", "solar-panel-2", "SolarPanel2", __iconNode);

export { __iconNode, IconSolarPanel2 as default };
//# sourceMappingURL=IconSolarPanel2.mjs.map
