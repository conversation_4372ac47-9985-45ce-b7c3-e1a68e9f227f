/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M18 16v2a1 1 0 0 1 -1 1h-11l6 -7l-6 -7h11a1 1 0 0 1 1 1v2", "key": "svg-0" }]];
const IconSum = createReactComponent("outline", "sum", "Sum", __iconNode);

export { __iconNode, IconSum as default };
//# sourceMappingURL=IconSum.mjs.map
