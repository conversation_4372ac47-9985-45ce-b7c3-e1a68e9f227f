/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M5 13a7 7 0 1 0 14 0a7 7 0 0 0 -14 0z", "key": "svg-0" }], ["path", { "d": "M14.5 10.5l-2.5 2.5", "key": "svg-1" }], ["path", { "d": "M17 8l1 -1", "key": "svg-2" }], ["path", { "d": "M14 3h-4", "key": "svg-3" }]];
const IconStopwatch = createReactComponent("outline", "stopwatch", "Stopwatch", __iconNode);

export { __iconNode, IconStopwatch as default };
//# sourceMappingURL=IconStopwatch.mjs.map
