/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M17 21v-6m2 0v-1.5m0 9v-1.5m-2 -3h3m-1 0h.5a1.5 1.5 0 0 1 0 3h-3.5m3 -3h.5a1.5 1.5 0 0 0 0 -3h-3.5", "key": "svg-0" }], ["path", { "d": "M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0", "key": "svg-1" }], ["path", { "d": "M6 21v-2a4 4 0 0 1 4 -4h3", "key": "svg-2" }]];
const IconUserBitcoin = createReactComponent("outline", "user-bitcoin", "UserBitcoin", __iconNode);

export { __iconNode, IconUserBitcoin as default };
//# sourceMappingURL=IconUserBitcoin.mjs.map
