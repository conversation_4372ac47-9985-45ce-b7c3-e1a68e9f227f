/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 18v3h16v-14l-8 -4l-8 4v3", "key": "svg-0" }], ["path", { "d": "M4 14h9", "key": "svg-1" }], ["path", { "d": "M10 11l3 3l-3 3", "key": "svg-2" }]];
const IconTransferIn = createReactComponent("outline", "transfer-in", "TransferIn", __iconNode);

export { __iconNode, IconTransferIn as default };
//# sourceMappingURL=IconTransferIn.mjs.map
