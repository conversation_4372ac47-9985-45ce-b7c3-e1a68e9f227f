/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M19 2a3 3 0 0 1 3 3v14a3 3 0 0 1 -3 3h-14a3 3 0 0 1 -3 -3v-14a3 3 0 0 1 3 -3zm-5 5a1 1 0 0 0 -1 1v6a1 1 0 0 1 -2 0v-6a1 1 0 0 0 -2 0v6a3 3 0 0 0 6 0v-6a1 1 0 0 0 -1 -1", "key": "svg-0" }]];
const IconSquareLetterUFilled = createReactComponent("filled", "square-letter-u-filled", "SquareLetterUFilled", __iconNode);

export { __iconNode, IconSquareLetterUFilled as default };
//# sourceMappingURL=IconSquareLetterUFilled.mjs.map
