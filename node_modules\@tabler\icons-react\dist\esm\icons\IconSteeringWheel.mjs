/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M12 12m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-1" }], ["path", { "d": "M12 14l0 7", "key": "svg-2" }], ["path", { "d": "M10 12l-6.75 -2", "key": "svg-3" }], ["path", { "d": "M14 12l6.75 -2", "key": "svg-4" }]];
const IconSteeringWheel = createReactComponent("outline", "steering-wheel", "SteeringWheel", __iconNode);

export { __iconNode, IconSteeringWheel as default };
//# sourceMappingURL=IconSteeringWheel.mjs.map
