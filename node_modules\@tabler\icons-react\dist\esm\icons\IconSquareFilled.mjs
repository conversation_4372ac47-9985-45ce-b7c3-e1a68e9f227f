/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M19 2h-14a3 3 0 0 0 -3 3v14a3 3 0 0 0 3 3h14a3 3 0 0 0 3 -3v-14a3 3 0 0 0 -3 -3z", "key": "svg-0" }]];
const IconSquareFilled = createReactComponent("filled", "square-filled", "SquareFilled", __iconNode);

export { __iconNode, IconSquareFilled as default };
//# sourceMappingURL=IconSquareFilled.mjs.map
