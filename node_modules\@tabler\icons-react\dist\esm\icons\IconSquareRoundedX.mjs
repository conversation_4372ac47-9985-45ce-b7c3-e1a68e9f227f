/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M10 10l4 4m0 -4l-4 4", "key": "svg-0" }], ["path", { "d": "M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z", "key": "svg-1" }]];
const IconSquareRoundedX = createReactComponent("outline", "square-rounded-x", "SquareRoundedX", __iconNode);

export { __iconNode, IconSquareRoundedX as default };
//# sourceMappingURL=IconSquareRoundedX.mjs.map
