/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M10 15l-3 3l-3 -3", "key": "svg-0" }], ["path", { "d": "M7 6v12", "key": "svg-1" }], ["path", { "d": "M14 18.333c0 .369 .298 .667 .667 .667h2.666a.667 .667 0 0 0 .667 -.667v-2.666a.667 .667 0 0 0 -.667 -.667h-2.666a.667 .667 0 0 0 -.667 .667v2.666z", "key": "svg-2" }], ["path", { "d": "M14 10.833c0 .645 .522 1.167 1.167 1.167h4.666c.645 0 1.167 -.522 1.167 -1.167v-4.666c0 -.645 -.522 -1.167 -1.167 -1.167h-4.666c-.645 0 -1.167 .522 -1.167 1.167v4.666z", "key": "svg-3" }]];
const IconSortDescendingSmallBig = createReactComponent("outline", "sort-descending-small-big", "SortDescendingSmallBig", __iconNode);

export { __iconNode, IconSortDescendingSmallBig as default };
//# sourceMappingURL=IconSortDescendingSmallBig.mjs.map
