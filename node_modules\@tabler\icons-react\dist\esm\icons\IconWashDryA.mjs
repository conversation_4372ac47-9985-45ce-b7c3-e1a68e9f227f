/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M9 16v-4.8c0 -1.657 1.343 -3.2 3 -3.2s3 1.543 3 3.2v4.8", "key": "svg-1" }], ["path", { "d": "M15 13h-6", "key": "svg-2" }]];
const IconWashDryA = createReactComponent("outline", "wash-dry-a", "WashDryA", __iconNode);

export { __iconNode, IconWashDryA as default };
//# sourceMappingURL=IconWashDryA.mjs.map
