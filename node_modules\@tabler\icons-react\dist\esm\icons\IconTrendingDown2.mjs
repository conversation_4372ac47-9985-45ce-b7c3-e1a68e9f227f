/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 6h5l7 10h6", "key": "svg-0" }], ["path", { "d": "M18 19l3 -3l-3 -3", "key": "svg-1" }]];
const IconTrendingDown2 = createReactComponent("outline", "trending-down-2", "TrendingDown2", __iconNode);

export { __iconNode, IconTrendingDown2 as default };
//# sourceMappingURL=IconTrendingDown2.mjs.map
