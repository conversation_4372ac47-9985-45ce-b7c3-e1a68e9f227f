/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M19 10h-14", "key": "svg-0" }], ["path", { "d": "M5 6h14", "key": "svg-1" }], ["path", { "d": "M14 14h-9", "key": "svg-2" }], ["path", { "d": "M5 18h6", "key": "svg-3" }], ["path", { "d": "M18 15v6", "key": "svg-4" }], ["path", { "d": "M15 18h6", "key": "svg-5" }]];
const IconTextPlus = createReactComponent("outline", "text-plus", "TextPlus", __iconNode);

export { __iconNode, IconTextPlus as default };
//# sourceMappingURL=IconTextPlus.mjs.map
