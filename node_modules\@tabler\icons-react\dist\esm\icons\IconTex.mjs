/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M9 8v-1h-6v1", "key": "svg-0" }], ["path", { "d": "M6 15v-8", "key": "svg-1" }], ["path", { "d": "M21 15l-5 -8", "key": "svg-2" }], ["path", { "d": "M16 15l5 -8", "key": "svg-3" }], ["path", { "d": "M14 11h-4v8h4", "key": "svg-4" }], ["path", { "d": "M10 15h3", "key": "svg-5" }]];
const IconTex = createReactComponent("outline", "tex", "Tex", __iconNode);

export { __iconNode, IconTex as default };
//# sourceMappingURL=IconTex.mjs.map
