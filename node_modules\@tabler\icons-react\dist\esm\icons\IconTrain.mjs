/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M21 13c0 -3.87 -3.37 -7 -10 -7h-8", "key": "svg-0" }], ["path", { "d": "M3 15h16a2 2 0 0 0 2 -2", "key": "svg-1" }], ["path", { "d": "M3 6v5h17.5", "key": "svg-2" }], ["path", { "d": "M3 11v4", "key": "svg-3" }], ["path", { "d": "M8 11v-5", "key": "svg-4" }], ["path", { "d": "M13 11v-4.5", "key": "svg-5" }], ["path", { "d": "M3 19h18", "key": "svg-6" }]];
const IconTrain = createReactComponent("outline", "train", "Train", __iconNode);

export { __iconNode, IconTrain as default };
//# sourceMappingURL=IconTrain.mjs.map
