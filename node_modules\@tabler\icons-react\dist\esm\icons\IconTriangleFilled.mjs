/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 1.67a2.914 2.914 0 0 0 -2.492 1.403l-8.11 13.537a2.914 2.914 0 0 0 2.484 4.385h16.225a2.914 2.914 0 0 0 2.503 -4.371l-8.116 -13.546a2.917 2.917 0 0 0 -2.494 -1.408z", "key": "svg-0" }]];
const IconTriangleFilled = createReactComponent("filled", "triangle-filled", "TriangleFilled", __iconNode);

export { __iconNode, IconTriangleFilled as default };
//# sourceMappingURL=IconTriangleFilled.mjs.map
