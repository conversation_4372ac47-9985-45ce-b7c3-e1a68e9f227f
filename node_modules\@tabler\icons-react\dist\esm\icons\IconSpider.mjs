/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M5 4v2l5 5", "key": "svg-0" }], ["path", { "d": "M2.5 9.5l1.5 1.5h6", "key": "svg-1" }], ["path", { "d": "M4 19v-2l6 -6", "key": "svg-2" }], ["path", { "d": "M19 4v2l-5 5", "key": "svg-3" }], ["path", { "d": "M21.5 9.5l-1.5 1.5h-6", "key": "svg-4" }], ["path", { "d": "M20 19v-2l-6 -6", "key": "svg-5" }], ["path", { "d": "M12 15m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0", "key": "svg-6" }], ["path", { "d": "M12 9m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-7" }]];
const IconSpider = createReactComponent("outline", "spider", "Spider", __iconNode);

export { __iconNode, IconSpider as default };
//# sourceMappingURL=IconSpider.mjs.map
