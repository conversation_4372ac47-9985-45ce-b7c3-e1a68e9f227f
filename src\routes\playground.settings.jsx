import { createFileRoute } from "@tanstack/react-router";

export const Route = createFileRoute("/playground/settings")({
  component: PlaygroundSettingsComponent,
});

function PlaygroundSettingsComponent() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Playground Settings</h1>
        <p className="text-muted-foreground">
          Configure your playground environment and preferences.
        </p>
      </div>
      
      <div className="grid gap-6">
        <div className="rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">Environment</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Debug Mode</label>
                <p className="text-xs text-muted-foreground">Enable detailed logging</p>
              </div>
              <input type="checkbox" className="rounded" />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Auto-save</label>
                <p className="text-xs text-muted-foreground">Automatically save changes</p>
              </div>
              <input type="checkbox" className="rounded" defaultChecked />
            </div>
          </div>
        </div>
        
        <div className="rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">Performance</h3>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium block mb-2">Execution Timeout (seconds)</label>
              <input 
                type="number" 
                defaultValue="30" 
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium block mb-2">Memory Limit (MB)</label>
              <input 
                type="number" 
                defaultValue="512" 
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
