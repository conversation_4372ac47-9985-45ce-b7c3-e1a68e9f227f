/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 4l-8 4l8 4l8 -4l-8 -4", "key": "svg-0" }], ["path", { "d": "M4 12l8 4l8 -4", "key": "svg-1" }], ["path", { "d": "M4 16l8 4l8 -4", "key": "svg-2" }]];
const IconStack2 = createReactComponent("outline", "stack-2", "Stack2", __iconNode);

export { __iconNode, IconStack2 as default };
//# sourceMappingURL=IconStack2.mjs.map
