/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4.326 19h15.348a1 1 0 0 0 .962 -1.275l-3.429 -12a1 1 0 0 0 -.961 -.725h-8.492a1 1 0 0 0 -.961 .725l-3.429 12a1 1 0 0 0 .962 1.275z", "key": "svg-0" }]];
const IconSkewY = createReactComponent("outline", "skew-y", "SkewY", __iconNode);

export { __iconNode, IconSkewY as default };
//# sourceMappingURL=IconSkewY.mjs.map
