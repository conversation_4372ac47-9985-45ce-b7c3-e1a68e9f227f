/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M7 11v8a1 1 0 0 1 -1 1h-2a1 1 0 0 1 -1 -1v-7a1 1 0 0 1 1 -1h3a3.987 3.987 0 0 0 2.828 -1.172m1.172 -2.828v-1a2 2 0 1 1 4 0v5h3a2 2 0 0 1 2 2c-.222 1.112 -.39 1.947 -.5 2.503m-.758 3.244c-.392 .823 -1.044 1.312 -1.742 1.253h-7a3 3 0 0 1 -3 -3", "key": "svg-0" }], ["path", { "d": "M3 3l18 18", "key": "svg-1" }]];
const IconThumbUpOff = createReactComponent("outline", "thumb-up-off", "ThumbUpOff", __iconNode);

export { __iconNode, IconThumbUpOff as default };
//# sourceMappingURL=IconThumbUpOff.mjs.map
