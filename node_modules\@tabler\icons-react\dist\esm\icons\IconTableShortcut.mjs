/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 13v-8a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-8", "key": "svg-0" }], ["path", { "d": "M3 10h18", "key": "svg-1" }], ["path", { "d": "M10 3v11", "key": "svg-2" }], ["path", { "d": "M2 22l5 -5", "key": "svg-3" }], ["path", { "d": "M7 21.5v-4.5h-4.5", "key": "svg-4" }]];
const IconTableShortcut = createReactComponent("outline", "table-shortcut", "TableShortcut", __iconNode);

export { __iconNode, IconTableShortcut as default };
//# sourceMappingURL=IconTableShortcut.mjs.map
