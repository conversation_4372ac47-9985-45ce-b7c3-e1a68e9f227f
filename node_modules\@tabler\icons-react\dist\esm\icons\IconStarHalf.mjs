/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 17.75l-6.172 3.245l1.179 -6.873l-5 -4.867l6.9 -1l3.086 -6.253z", "key": "svg-0" }]];
const IconStarHalf = createReactComponent("outline", "star-half", "StarHalf", __iconNode);

export { __iconNode, IconStarHalf as default };
//# sourceMappingURL=IconStarHalf.mjs.map
