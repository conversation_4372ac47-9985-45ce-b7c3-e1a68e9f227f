/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M10 3a1 1 0 1 0 2 0a1 1 0 0 0 -2 0", "key": "svg-0" }], ["path", { "d": "M3 14l4 1l.5 -.5", "key": "svg-1" }], ["path", { "d": "M12 18v-3l-3 -2.923l.75 -5.077", "key": "svg-2" }], ["path", { "d": "M6 10v-2l4 -1l2.5 2.5l2.5 .5", "key": "svg-3" }], ["path", { "d": "M21 22a1 1 0 0 0 -1 -1h-16a1 1 0 0 0 -1 1", "key": "svg-4" }], ["path", { "d": "M18 21l1 -11l2 -1", "key": "svg-5" }]];
const IconTreadmill = createReactComponent("outline", "treadmill", "Treadmill", __iconNode);

export { __iconNode, IconTreadmill as default };
//# sourceMappingURL=IconTreadmill.mjs.map
