/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M16 8h4l-4 8h4", "key": "svg-0" }], ["path", { "d": "M4 16v-6a2 2 0 1 1 4 0v6", "key": "svg-1" }], ["path", { "d": "M4 13h4", "key": "svg-2" }], ["path", { "d": "M11 12h2", "key": "svg-3" }]];
const IconSortAZ = createReactComponent("outline", "sort-a-z", "SortAZ", __iconNode);

export { __iconNode, IconSortAZ as default };
//# sourceMappingURL=IconSortAZ.mjs.map
