/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 21a9 9 0 0 0 9 -9a9 9 0 0 0 -9 -9a9 9 0 0 0 -9 9a9 9 0 0 0 9 9z", "key": "svg-0" }], ["path", { "d": "M9 8l6 8", "key": "svg-1" }], ["path", { "d": "M15 8l-6 8", "key": "svg-2" }]];
const IconXboxX = createReactComponent("outline", "xbox-x", "XboxX", __iconNode);

export { __iconNode, IconXboxX as default };
//# sourceMappingURL=IconXboxX.mjs.map
