/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 3m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z", "key": "svg-0" }], ["path", { "d": "M13 9v2a1 1 0 0 0 1 1h1", "key": "svg-1" }], ["path", { "d": "M16 9v6", "key": "svg-2" }], ["path", { "d": "M8 12h2", "key": "svg-3" }], ["path", { "d": "M10 9h-2v6", "key": "svg-4" }]];
const IconSquareF4 = createReactComponent("outline", "square-f4", "SquareF4", __iconNode);

export { __iconNode, IconSquareF4 as default };
//# sourceMappingURL=IconSquareF4.mjs.map
