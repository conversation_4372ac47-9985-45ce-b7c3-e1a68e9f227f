/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 5.205v13.59a1 1 0 0 0 1.184 .983l14 -2.625a1 1 0 0 0 .816 -.983v-8.34a1 1 0 0 0 -.816 -.983l-14 -2.625a1 1 0 0 0 -1.184 .983z", "key": "svg-0" }]];
const IconSkewX = createReactComponent("outline", "skew-x", "SkewX", __iconNode);

export { __iconNode, IconSkewX as default };
//# sourceMappingURL=IconSkewX.mjs.map
