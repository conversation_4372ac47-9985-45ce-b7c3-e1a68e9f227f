/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0", "key": "svg-0" }], ["path", { "d": "M7 10l6 0", "key": "svg-1" }], ["path", { "d": "M10 7l0 6", "key": "svg-2" }], ["path", { "d": "M21 21l-6 -6", "key": "svg-3" }]];
const IconZoomIn = createReactComponent("outline", "zoom-in", "ZoomIn", __iconNode);

export { __iconNode, IconZoomIn as default };
//# sourceMappingURL=IconZoomIn.mjs.map
