/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M8 9l3 3l-3 3", "key": "svg-0" }], ["path", { "d": "M13 9l3 3l-3 3", "key": "svg-1" }], ["path", { "d": "M3 5a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-14z", "key": "svg-2" }]];
const IconSquareChevronsRight = createReactComponent("outline", "square-chevrons-right", "SquareChevronsRight", __iconNode);

export { __iconNode, IconSquareChevronsRight as default };
//# sourceMappingURL=IconSquareChevronsRight.mjs.map
