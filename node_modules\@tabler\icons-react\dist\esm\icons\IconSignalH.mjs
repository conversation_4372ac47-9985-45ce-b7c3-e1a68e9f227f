/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M10 16v-8", "key": "svg-0" }], ["path", { "d": "M14 8v8", "key": "svg-1" }], ["path", { "d": "M10 12h4", "key": "svg-2" }]];
const IconSignalH = createReactComponent("outline", "signal-h", "SignalH", __iconNode);

export { __iconNode, IconSignalH as default };
//# sourceMappingURL=IconSignalH.mjs.map
