/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 3m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z", "key": "svg-0" }], ["path", { "d": "M13 11l2 -2v6", "key": "svg-1" }], ["path", { "d": "M8 12h2", "key": "svg-2" }], ["path", { "d": "M10 9h-2v6", "key": "svg-3" }]];
const IconSquareF1 = createReactComponent("outline", "square-f1", "SquareF1", __iconNode);

export { __iconNode, IconSquareF1 as default };
//# sourceMappingURL=IconSquareF1.mjs.map
