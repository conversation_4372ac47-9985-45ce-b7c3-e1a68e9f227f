/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 6l-8 4l8 4l8 -4l-8 -4", "key": "svg-0" }], ["path", { "d": "M4 14l8 4l8 -4", "key": "svg-1" }]];
const IconStack = createReactComponent("outline", "stack", "Stack", __iconNode);

export { __iconNode, IconStack as default };
//# sourceMappingURL=IconStack.mjs.map
