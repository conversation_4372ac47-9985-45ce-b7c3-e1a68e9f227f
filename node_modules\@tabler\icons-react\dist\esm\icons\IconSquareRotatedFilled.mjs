/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M9.793 2.893l-6.9 6.9c-1.172 1.171 -1.172 3.243 0 4.414l6.9 6.9c1.171 1.172 3.243 1.172 4.414 0l6.9 -6.9c1.172 -1.171 1.172 -3.243 0 -4.414l-6.9 -6.9c-1.171 -1.172 -3.243 -1.172 -4.414 0z", "key": "svg-0" }]];
const IconSquareRotatedFilled = createReactComponent("filled", "square-rotated-filled", "SquareRotatedFilled", __iconNode);

export { __iconNode, IconSquareRotatedFilled as default };
//# sourceMappingURL=IconSquareRotatedFilled.mjs.map
