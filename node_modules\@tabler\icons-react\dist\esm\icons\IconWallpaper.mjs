/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M8 6h10a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-12", "key": "svg-0" }], ["path", { "d": "M6 18m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-1" }], ["path", { "d": "M8 18v-12a2 2 0 1 0 -4 0v12", "key": "svg-2" }]];
const IconWallpaper = createReactComponent("outline", "wallpaper", "Wallpaper", __iconNode);

export { __iconNode, IconWallpaper as default };
//# sourceMappingURL=IconWallpaper.mjs.map
