/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M20.884 10.554a9 9 0 1 0 -10.337 10.328", "key": "svg-0" }], ["path", { "d": "M3.6 9h16.8", "key": "svg-1" }], ["path", { "d": "M3.6 15h6.9", "key": "svg-2" }], ["path", { "d": "M11.5 3a17 17 0 0 0 -1.502 14.954", "key": "svg-3" }], ["path", { "d": "M12.5 3a17 17 0 0 1 2.52 7.603", "key": "svg-4" }], ["path", { "d": "M18 18m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0", "key": "svg-5" }], ["path", { "d": "M18 16.5v1.5l.5 .5", "key": "svg-6" }]];
const IconTimezone = createReactComponent("outline", "timezone", "Timezone", __iconNode);

export { __iconNode, IconTimezone as default };
//# sourceMappingURL=IconTimezone.mjs.map
