/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 3a5 5 0 0 1 1.001 9.9l-.001 4.684l1.293 -1.291a1 1 0 0 1 1.32 -.083l.094 .083a1 1 0 0 1 0 1.414l-3 3a1 1 0 0 1 -.112 .097l-.11 .071l-.114 .054l-.105 .035l-.149 .03l-.117 .006l-.075 -.003l-.126 -.017l-.111 -.03l-.111 -.044l-.098 -.052l-.096 -.067l-.09 -.08l-3 -3a1 1 0 0 1 1.414 -1.414l1.293 1.292v-4.685a5.002 5.002 0 0 1 1 -9.9", "key": "svg-0" }]];
const IconSwipeDownFilled = createReactComponent("filled", "swipe-down-filled", "SwipeDownFilled", __iconNode);

export { __iconNode, IconSwipeDownFilled as default };
//# sourceMappingURL=IconSwipeDownFilled.mjs.map
