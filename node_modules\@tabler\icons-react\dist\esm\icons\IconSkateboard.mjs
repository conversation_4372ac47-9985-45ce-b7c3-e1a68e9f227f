/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M7 15m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-0" }], ["path", { "d": "M17 15m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-1" }], ["path", { "d": "M3 9a2 1 0 0 0 2 1h14a2 1 0 0 0 2 -1", "key": "svg-2" }]];
const IconSkateboard = createReactComponent("outline", "skateboard", "Skateboard", __iconNode);

export { __iconNode, IconSkateboard as default };
//# sourceMappingURL=IconSkateboard.mjs.map
