/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 5h18", "key": "svg-0" }], ["path", { "d": "M3 19h18", "key": "svg-1" }], ["path", { "d": "M4 9l2 6h1l2 -6", "key": "svg-2" }], ["path", { "d": "M12 9v6", "key": "svg-3" }], ["path", { "d": "M16 15v-6h2a2 2 0 1 1 0 4h-2", "key": "svg-4" }]];
const IconVip = createReactComponent("outline", "vip", "Vip", __iconNode);

export { __iconNode, IconVip as default };
//# sourceMappingURL=IconVip.mjs.map
