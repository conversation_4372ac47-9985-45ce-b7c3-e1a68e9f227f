/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 12c0 -1.657 1.592 -3 3.556 -3c1.963 0 3.11 1.5 4.444 3c1.333 1.5 2.48 3 4.444 3s3.556 -1.343 3.556 -3", "key": "svg-0" }]];
const IconTilde = createReactComponent("outline", "tilde", "Tilde", __iconNode);

export { __iconNode, IconTilde as default };
//# sourceMappingURL=IconTilde.mjs.map
