/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M10 10v3.5a4 4 0 1 0 5.836 2.33m-1.836 -5.83v-5a2 2 0 1 0 -4 0v1", "key": "svg-0" }], ["path", { "d": "M13 9h1", "key": "svg-1" }], ["path", { "d": "M3 3l18 18", "key": "svg-2" }]];
const IconTemperatureOff = createReactComponent("outline", "temperature-off", "TemperatureOff", __iconNode);

export { __iconNode, IconTemperatureOff as default };
//# sourceMappingURL=IconTemperatureOff.mjs.map
