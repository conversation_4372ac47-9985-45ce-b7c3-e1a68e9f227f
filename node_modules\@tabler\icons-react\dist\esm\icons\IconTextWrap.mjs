/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 6l16 0", "key": "svg-0" }], ["path", { "d": "M4 18l5 0", "key": "svg-1" }], ["path", { "d": "M4 12h13a3 3 0 0 1 0 6h-4l2 -2m0 4l-2 -2", "key": "svg-2" }]];
const IconTextWrap = createReactComponent("outline", "text-wrap", "TextWrap", __iconNode);

export { __iconNode, IconTextWrap as default };
//# sourceMappingURL=IconTextWrap.mjs.map
