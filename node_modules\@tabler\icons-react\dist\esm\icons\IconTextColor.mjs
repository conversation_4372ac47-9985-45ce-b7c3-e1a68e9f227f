/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M9 15v-7a3 3 0 0 1 6 0v7", "key": "svg-0" }], ["path", { "d": "M9 11h6", "key": "svg-1" }], ["path", { "d": "M5 19h14", "key": "svg-2" }]];
const IconTextColor = createReactComponent("outline", "text-color", "TextColor", __iconNode);

export { __iconNode, IconTextColor as default };
//# sourceMappingURL=IconTextColor.mjs.map
