/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0", "key": "svg-0" }], ["path", { "d": "M6 21v-2a4 4 0 0 1 4 -4h3.5", "key": "svg-1" }], ["path", { "d": "M18.42 15.61a2.1 2.1 0 0 1 2.97 2.97l-3.39 3.42h-3v-3l3.42 -3.39z", "key": "svg-2" }]];
const IconUserEdit = createReactComponent("outline", "user-edit", "UserEdit", __iconNode);

export { __iconNode, IconUserEdit as default };
//# sourceMappingURL=IconUserEdit.mjs.map
