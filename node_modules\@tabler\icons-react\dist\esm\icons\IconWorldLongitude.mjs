/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M11.5 3a11.2 11.2 0 0 0 0 18", "key": "svg-1" }], ["path", { "d": "M12.5 3a11.2 11.2 0 0 1 0 18", "key": "svg-2" }], ["path", { "d": "M12 3l0 18", "key": "svg-3" }]];
const IconWorldLongitude = createReactComponent("outline", "world-longitude", "WorldLongitude", __iconNode);

export { __iconNode, IconWorldLongitude as default };
//# sourceMappingURL=IconWorldLongitude.mjs.map
