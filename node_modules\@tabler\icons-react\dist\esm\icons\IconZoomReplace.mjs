/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M21 21l-6 -6", "key": "svg-0" }], ["path", { "d": "M3.291 8a7 7 0 0 1 5.077 -4.806a7.021 7.021 0 0 1 8.242 4.403", "key": "svg-1" }], ["path", { "d": "M17 4v4h-4", "key": "svg-2" }], ["path", { "d": "M16.705 12a7 7 0 0 1 -5.074 4.798a7.021 7.021 0 0 1 -8.241 -4.403", "key": "svg-3" }], ["path", { "d": "M3 16v-4h4", "key": "svg-4" }]];
const IconZoomReplace = createReactComponent("outline", "zoom-replace", "ZoomReplace", __iconNode);

export { __iconNode, IconZoomReplace as default };
//# sourceMappingURL=IconZoomReplace.mjs.map
