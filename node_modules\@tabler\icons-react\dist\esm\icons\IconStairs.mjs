/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M22 5h-5v5h-5v5h-5v5h-5", "key": "svg-0" }]];
const IconStairs = createReactComponent("outline", "stairs", "Stairs", __iconNode);

export { __iconNode, IconStairs as default };
//# sourceMappingURL=IconStairs.mjs.map
