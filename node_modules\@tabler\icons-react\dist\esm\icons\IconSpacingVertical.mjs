/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 20v-2a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v2", "key": "svg-0" }], ["path", { "d": "M4 4v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2", "key": "svg-1" }], ["path", { "d": "M16 12h-8", "key": "svg-2" }]];
const IconSpacingVertical = createReactComponent("outline", "spacing-vertical", "SpacingVertical", __iconNode);

export { __iconNode, IconSpacingVertical as default };
//# sourceMappingURL=IconSpacingVertical.mjs.map
