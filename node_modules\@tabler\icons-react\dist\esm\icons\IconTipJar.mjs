/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M14 10h-2.5a1.5 1.5 0 0 0 0 3h1a1.5 1.5 0 0 1 0 3h-2.5", "key": "svg-0" }], ["path", { "d": "M12 9v1", "key": "svg-1" }], ["path", { "d": "M12 16v1", "key": "svg-2" }], ["path", { "d": "M17 4v1.882c0 .685 .387 1.312 1 1.618s1 .933 1 1.618v8.882a3 3 0 0 1 -3 3h-8a3 3 0 0 1 -3 -3v-8.882c0 -.685 .387 -1.312 1 -1.618s1 -.933 1 -1.618v-1.882", "key": "svg-3" }], ["path", { "d": "M6 4h12z", "key": "svg-4" }]];
const IconTipJar = createReactComponent("outline", "tip-jar", "TipJar", __iconNode);

export { __iconNode, IconTipJar as default };
//# sourceMappingURL=IconTipJar.mjs.map
