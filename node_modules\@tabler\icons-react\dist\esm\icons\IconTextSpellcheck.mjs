/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M5 15v-7.5a3.5 3.5 0 0 1 7 0v7.5", "key": "svg-0" }], ["path", { "d": "M5 10h7", "key": "svg-1" }], ["path", { "d": "M10 18l3 3l7 -7", "key": "svg-2" }]];
const IconTextSpellcheck = createReactComponent("outline", "text-spellcheck", "TextSpellcheck", __iconNode);

export { __iconNode, IconTextSpellcheck as default };
//# sourceMappingURL=IconTextSpellcheck.mjs.map
