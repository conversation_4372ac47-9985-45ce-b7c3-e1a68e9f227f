/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M2 12m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v0a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z", "key": "svg-0" }], ["path", { "d": "M6 12l1 -5h5l3 5", "key": "svg-1" }], ["path", { "d": "M21 9l-7.8 0", "key": "svg-2" }]];
const IconTank = createReactComponent("outline", "tank", "Tank", __iconNode);

export { __iconNode, IconTank as default };
//# sourceMappingURL=IconTank.mjs.map
