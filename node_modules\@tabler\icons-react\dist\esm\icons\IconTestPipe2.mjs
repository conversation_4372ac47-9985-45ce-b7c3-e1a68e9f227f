/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M15 3v15a3 3 0 0 1 -6 0v-15", "key": "svg-0" }], ["path", { "d": "M9 12h6", "key": "svg-1" }], ["path", { "d": "M8 3h8", "key": "svg-2" }]];
const IconTestPipe2 = createReactComponent("outline", "test-pipe-2", "TestPipe2", __iconNode);

export { __iconNode, IconTestPipe2 as default };
//# sourceMappingURL=IconTestPipe2.mjs.map
