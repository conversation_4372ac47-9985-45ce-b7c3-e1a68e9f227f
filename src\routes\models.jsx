import { createFileRoute } from "@tanstack/react-router";

export const Route = createFileRoute("/models")({
  component: ModelsComponent,
});

function ModelsComponent() {
  const models = [
    {
      id: 1,
      name: "GPT-4",
      status: "Active",
      type: "Language Model",
      lastUsed: "2 hours ago",
    },
    {
      id: 2,
      name: "Claude-3",
      status: "Active",
      type: "Language Model",
      lastUsed: "1 day ago",
    },
    {
      id: 3,
      name: "DALL-E 3",
      status: "Inactive",
      type: "Image Generation",
      lastUsed: "1 week ago",
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Models</h1>
          <p className="text-muted-foreground">
            Manage and configure your AI models.
          </p>
        </div>
        <button className="rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90">
          Add Model
        </button>
      </div>
      
      <div className="rounded-lg border">
        <div className="p-6">
          <h3 className="text-lg font-semibold mb-4">Available Models</h3>
          <div className="space-y-4">
            {models.map((model) => (
              <div key={model.id} className="flex items-center justify-between p-4 rounded-lg border">
                <div className="space-y-1">
                  <h4 className="font-medium">{model.name}</h4>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>{model.type}</span>
                    <span>•</span>
                    <span>Last used {model.lastUsed}</span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                    model.status === 'Active' 
                      ? 'bg-green-100 text-green-700' 
                      : 'bg-gray-100 text-gray-700'
                  }`}>
                    {model.status}
                  </span>
                  <button className="rounded-md border border-input bg-background px-3 py-1 text-sm hover:bg-accent hover:text-accent-foreground">
                    Configure
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
