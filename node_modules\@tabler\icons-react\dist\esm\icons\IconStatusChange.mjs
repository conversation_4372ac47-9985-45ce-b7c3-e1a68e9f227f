/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M6 18m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-0" }], ["path", { "d": "M18 18m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-1" }], ["path", { "d": "M6 12v-2a6 6 0 1 1 12 0v2", "key": "svg-2" }], ["path", { "d": "M15 9l3 3l3 -3", "key": "svg-3" }]];
const IconStatusChange = createReactComponent("outline", "status-change", "StatusChange", __iconNode);

export { __iconNode, IconStatusChange as default };
//# sourceMappingURL=IconStatusChange.mjs.map
