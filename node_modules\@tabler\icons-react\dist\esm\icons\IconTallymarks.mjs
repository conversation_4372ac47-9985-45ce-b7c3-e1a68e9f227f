/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M6 5l0 14", "key": "svg-0" }], ["path", { "d": "M10 5l0 14", "key": "svg-1" }], ["path", { "d": "M14 5l0 14", "key": "svg-2" }], ["path", { "d": "M18 5l0 14", "key": "svg-3" }], ["path", { "d": "M3 17l18 -10", "key": "svg-4" }]];
const IconTallymarks = createReactComponent("outline", "tallymarks", "Tallymarks", __iconNode);

export { __iconNode, IconTallymarks as default };
//# sourceMappingURL=IconTallymarks.mjs.map
