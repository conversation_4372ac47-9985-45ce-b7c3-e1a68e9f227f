/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 6l10 0", "key": "svg-0" }], ["path", { "d": "M4 18l10 0", "key": "svg-1" }], ["path", { "d": "M4 12h17l-3 -3m0 6l3 -3", "key": "svg-2" }]];
const IconTextWrapDisabled = createReactComponent("outline", "text-wrap-disabled", "TextWrapDisabled", __iconNode);

export { __iconNode, IconTextWrapDisabled as default };
//# sourceMappingURL=IconTextWrapDisabled.mjs.map
