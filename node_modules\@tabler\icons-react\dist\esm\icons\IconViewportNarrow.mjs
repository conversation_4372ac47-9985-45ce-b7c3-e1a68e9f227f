/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 12h7l-3 -3", "key": "svg-0" }], ["path", { "d": "M7 15l3 -3", "key": "svg-1" }], ["path", { "d": "M21 12h-7l3 -3", "key": "svg-2" }], ["path", { "d": "M17 15l-3 -3", "key": "svg-3" }], ["path", { "d": "M9 6v-1a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1", "key": "svg-4" }], ["path", { "d": "M9 18v1a2 2 0 0 0 2 2h2a2 2 0 0 0 2 -2v-1", "key": "svg-5" }]];
const IconViewportNarrow = createReactComponent("outline", "viewport-narrow", "ViewportNarrow", __iconNode);

export { __iconNode, IconViewportNarrow as default };
//# sourceMappingURL=IconViewportNarrow.mjs.map
