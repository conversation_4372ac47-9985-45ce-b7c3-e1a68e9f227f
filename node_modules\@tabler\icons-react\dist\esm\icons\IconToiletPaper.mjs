/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M6 10m-3 0a3 7 0 1 0 6 0a3 7 0 1 0 -6 0", "key": "svg-0" }], ["path", { "d": "M21 10c0 -3.866 -1.343 -7 -3 -7", "key": "svg-1" }], ["path", { "d": "M6 3h12", "key": "svg-2" }], ["path", { "d": "M21 10v10l-3 -1l-3 2l-3 -3l-3 2v-10", "key": "svg-3" }], ["path", { "d": "M6 10h.01", "key": "svg-4" }]];
const IconToiletPaper = createReactComponent("outline", "toilet-paper", "ToiletPaper", __iconNode);

export { __iconNode, IconToiletPaper as default };
//# sourceMappingURL=IconToiletPaper.mjs.map
