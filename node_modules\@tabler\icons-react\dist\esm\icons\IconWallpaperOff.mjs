/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M10 6h8a2 2 0 0 1 2 2v8m-.58 3.409a2 2 0 0 1 -1.42 .591h-12", "key": "svg-0" }], ["path", { "d": "M6 18m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-1" }], ["path", { "d": "M8 18v-10m-3.427 -3.402c-.353 .362 -.573 .856 -.573 1.402v12", "key": "svg-2" }], ["path", { "d": "M3 3l18 18", "key": "svg-3" }]];
const IconWallpaperOff = createReactComponent("outline", "wallpaper-off", "WallpaperOff", __iconNode);

export { __iconNode, IconWallpaperOff as default };
//# sourceMappingURL=IconWallpaperOff.mjs.map
