/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0", "key": "svg-0" }], ["path", { "d": "M17 17l-2.5 -2.5", "key": "svg-1" }], ["path", { "d": "M10 4l2 -2l2 2", "key": "svg-2" }], ["path", { "d": "M20 10l2 2l-2 2", "key": "svg-3" }], ["path", { "d": "M4 10l-2 2l2 2", "key": "svg-4" }], ["path", { "d": "M10 20l2 2l2 -2", "key": "svg-5" }]];
const IconZoomPan = createReactComponent("outline", "zoom-pan", "ZoomPan", __iconNode);

export { __iconNode, IconZoomPan as default };
//# sourceMappingURL=IconZoomPan.mjs.map
