/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M6 21a3 3 0 0 1 -3 -3v-12a3 3 0 0 1 3 -3", "key": "svg-0" }], ["path", { "d": "M21 6v12a3 3 0 0 1 -6 0v-12a3 3 0 0 1 6 0z", "key": "svg-1" }], ["path", { "d": "M15 12h-8", "key": "svg-2" }], ["path", { "d": "M10 9l-3 3l3 3", "key": "svg-3" }]];
const IconTransitionLeft = createReactComponent("outline", "transition-left", "TransitionLeft", __iconNode);

export { __iconNode, IconTransitionLeft as default };
//# sourceMappingURL=IconTransitionLeft.mjs.map
