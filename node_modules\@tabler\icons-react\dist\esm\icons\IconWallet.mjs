/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M17 8v-3a1 1 0 0 0 -1 -1h-10a2 2 0 0 0 0 4h12a1 1 0 0 1 1 1v3m0 4v3a1 1 0 0 1 -1 1h-12a2 2 0 0 1 -2 -2v-12", "key": "svg-0" }], ["path", { "d": "M20 12v4h-4a2 2 0 0 1 0 -4h4", "key": "svg-1" }]];
const IconWallet = createReactComponent("outline", "wallet", "Wallet", __iconNode);

export { __iconNode, IconWallet as default };
//# sourceMappingURL=IconWallet.mjs.map
