/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 12a1 1 0 0 1 0 2h-1a1 1 0 0 1 0 -2z", "key": "svg-0" }], ["path", { "d": "M21 12a1 1 0 0 1 0 2h-1a1 1 0 0 1 0 -2z", "key": "svg-1" }], ["path", { "d": "M6.307 5.893l.7 .7a1 1 0 0 1 -1.414 1.414l-.7 -.7a1 1 0 0 1 1.414 -1.414", "key": "svg-2" }], ["path", { "d": "M19.107 5.893a1 1 0 0 1 0 1.414l-.7 .7a1 1 0 0 1 -1.414 -1.414l.7 -.7a1 1 0 0 1 1.414 0", "key": "svg-3" }], ["path", { "d": "M12 3a1 1 0 0 1 1 1v1a1 1 0 0 1 -2 0v-1a1 1 0 0 1 1 -1", "key": "svg-4" }], ["path", { "d": "M3 16h18a1 1 0 0 1 0 2h-18a1 1 0 0 1 0 -2", "key": "svg-5" }], ["path", { "d": "M12 8a5 5 0 0 1 4.583 7.002h-9.166a5 5 0 0 1 4.583 -7.002", "key": "svg-6" }], ["path", { "d": "M12 19a1 1 0 0 1 0 2h-5a1 1 0 0 1 0 -2z", "key": "svg-7" }], ["path", { "d": "M17 19a1 1 0 0 1 0 2h-1a1 1 0 0 1 0 -2z", "key": "svg-8" }]];
const IconSunset2Filled = createReactComponent("filled", "sunset-2-filled", "Sunset2Filled", __iconNode);

export { __iconNode, IconSunset2Filled as default };
//# sourceMappingURL=IconSunset2Filled.mjs.map
