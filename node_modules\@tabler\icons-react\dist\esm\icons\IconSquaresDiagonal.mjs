/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M8 8m0 2a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2z", "key": "svg-0" }], ["path", { "d": "M16 8v-2a2 2 0 0 0 -2 -2h-8a2 2 0 0 0 -2 2v8a2 2 0 0 0 2 2h2", "key": "svg-1" }], ["path", { "d": "M8.586 19.414l10.827 -10.827", "key": "svg-2" }]];
const IconSquaresDiagonal = createReactComponent("outline", "squares-diagonal", "SquaresDiagonal", __iconNode);

export { __iconNode, IconSquaresDiagonal as default };
//# sourceMappingURL=IconSquaresDiagonal.mjs.map
