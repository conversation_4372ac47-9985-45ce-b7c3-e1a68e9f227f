/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M21 12a9 9 0 1 1 -18 0a9 9 0 0 1 18 0z", "key": "svg-0" }], ["path", { "d": "M14 9a2 2 0 1 1 -4 0a2 2 0 0 1 4 0z", "key": "svg-1" }], ["path", { "d": "M12 16h.01", "key": "svg-2" }]];
const IconVacuumCleaner = createReactComponent("outline", "vacuum-cleaner", "VacuumCleaner", __iconNode);

export { __iconNode, IconVacuumCleaner as default };
//# sourceMappingURL=IconVacuumCleaner.mjs.map
