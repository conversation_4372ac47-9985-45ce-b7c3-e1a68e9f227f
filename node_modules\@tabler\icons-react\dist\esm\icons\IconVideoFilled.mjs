/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M20.117 7.625a1 1 0 0 0 -.564 .1l-4.553 2.275v4l4.553 2.275a1 1 0 0 0 1.447 -.892v-6.766a1 1 0 0 0 -.883 -.992z", "key": "svg-0" }], ["path", { "d": "M5 5c-1.645 0 -3 1.355 -3 3v8c0 1.645 1.355 3 3 3h8c1.645 0 3 -1.355 3 -3v-8c0 -1.645 -1.355 -3 -3 -3z", "key": "svg-1" }]];
const IconVideoFilled = createReactComponent("filled", "video-filled", "VideoFilled", __iconNode);

export { __iconNode, IconVideoFilled as default };
//# sourceMappingURL=IconVideoFilled.mjs.map
