/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M7 5v5a5 5 0 0 0 10 0v-5", "key": "svg-0" }], ["path", { "d": "M5 19h14", "key": "svg-1" }]];
const IconUnderline = createReactComponent("outline", "underline", "Underline", __iconNode);

export { __iconNode, IconUnderline as default };
//# sourceMappingURL=IconUnderline.mjs.map
