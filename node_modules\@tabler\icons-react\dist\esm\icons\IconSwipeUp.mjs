/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 16m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0", "key": "svg-0" }], ["path", { "d": "M12 12v-8", "key": "svg-1" }], ["path", { "d": "M9 7l3 -3l3 3", "key": "svg-2" }]];
const IconSwipeUp = createReactComponent("outline", "swipe-up", "SwipeUp", __iconNode);

export { __iconNode, IconSwipeUp as default };
//# sourceMappingURL=IconSwipeUp.mjs.map
