/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M19.03 17.818a3 3 0 0 0 1.97 -2.818v-8a3 3 0 0 0 -3 -3h-12a3 3 0 0 0 -3 3v8c0 1.317 .85 2.436 2.03 2.84", "key": "svg-0" }], ["path", { "d": "M10 14a2 2 0 1 0 4 0a2 2 0 0 0 -4 0", "key": "svg-1" }], ["path", { "d": "M8 21a2 2 0 0 1 2 -2h4a2 2 0 0 1 2 2", "key": "svg-2" }]];
const IconUserScreen = createReactComponent("outline", "user-screen", "UserScreen", __iconNode);

export { __iconNode, IconUserScreen as default };
//# sourceMappingURL=IconUserScreen.mjs.map
