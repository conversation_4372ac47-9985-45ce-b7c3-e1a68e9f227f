/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M11.132 2.504l-4 7a1 1 0 0 0 .868 1.496h8a1 1 0 0 0 .868 -1.496l-4 -7a1 1 0 0 0 -1.736 0z", "key": "svg-0" }], ["path", { "d": "M17 13a4 4 0 1 1 -3.995 4.2l-.005 -.2l.005 -.2a4 4 0 0 1 3.995 -3.8z", "key": "svg-1" }], ["path", { "d": "M9 13h-4a2 2 0 0 0 -2 2v4a2 2 0 0 0 2 2h4a2 2 0 0 0 2 -2v-4a2 2 0 0 0 -2 -2z", "key": "svg-2" }]];
const IconTriangleSquareCircleFilled = createReactComponent("filled", "triangle-square-circle-filled", "TriangleSquareCircleFilled", __iconNode);

export { __iconNode, IconTriangleSquareCircleFilled as default };
//# sourceMappingURL=IconTriangleSquareCircleFilled.mjs.map
