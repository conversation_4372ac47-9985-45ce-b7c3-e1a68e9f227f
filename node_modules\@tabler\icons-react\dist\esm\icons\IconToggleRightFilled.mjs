/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M16 9a3 3 0 1 1 -3 3l.005 -.176a3 3 0 0 1 2.995 -2.824", "key": "svg-0" }], ["path", { "d": "M16 5a7 7 0 0 1 0 14h-8a7 7 0 0 1 0 -14zm0 2h-8a5 5 0 1 0 0 10h8a5 5 0 0 0 0 -10", "key": "svg-1" }]];
const IconToggleRightFilled = createReactComponent("filled", "toggle-right-filled", "ToggleRightFilled", __iconNode);

export { __iconNode, IconToggleRightFilled as default };
//# sourceMappingURL=IconToggleRightFilled.mjs.map
