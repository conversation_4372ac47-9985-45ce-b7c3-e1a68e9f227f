/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M12 3l0 4", "key": "svg-1" }], ["path", { "d": "M12 21l0 -3", "key": "svg-2" }], ["path", { "d": "M3 12l4 0", "key": "svg-3" }], ["path", { "d": "M21 12l-3 0", "key": "svg-4" }], ["path", { "d": "M12 12l0 .01", "key": "svg-5" }]];
const IconViewfinder = createReactComponent("outline", "viewfinder", "Viewfinder", __iconNode);

export { __iconNode, IconViewfinder as default };
//# sourceMappingURL=IconViewfinder.mjs.map
