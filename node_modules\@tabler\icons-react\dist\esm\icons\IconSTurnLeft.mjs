/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M19 7a2 2 0 1 1 0 -4a2 2 0 0 1 0 4z", "key": "svg-0" }], ["path", { "d": "M17 5h-9.5a3.5 3.5 0 0 0 0 7h9a3.5 3.5 0 0 1 0 7h-13.5", "key": "svg-1" }], ["path", { "d": "M6 16l-3 3l3 3", "key": "svg-2" }]];
const IconSTurnLeft = createReactComponent("outline", "s-turn-left", "STurnLeft", __iconNode);

export { __iconNode, IconSTurnLeft as default };
//# sourceMappingURL=IconSTurnLeft.mjs.map
