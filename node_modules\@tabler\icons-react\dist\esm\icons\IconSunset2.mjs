/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 13h1", "key": "svg-0" }], ["path", { "d": "M20 13h1", "key": "svg-1" }], ["path", { "d": "M5.6 6.6l.7 .7", "key": "svg-2" }], ["path", { "d": "M18.4 6.6l-.7 .7", "key": "svg-3" }], ["path", { "d": "M8 13a4 4 0 1 1 8 0", "key": "svg-4" }], ["path", { "d": "M3 17h18", "key": "svg-5" }], ["path", { "d": "M7 20h5", "key": "svg-6" }], ["path", { "d": "M16 20h1", "key": "svg-7" }], ["path", { "d": "M12 5v-1", "key": "svg-8" }]];
const IconSunset2 = createReactComponent("outline", "sunset-2", "Sunset2", __iconNode);

export { __iconNode, IconSunset2 as default };
//# sourceMappingURL=IconSunset2.mjs.map
