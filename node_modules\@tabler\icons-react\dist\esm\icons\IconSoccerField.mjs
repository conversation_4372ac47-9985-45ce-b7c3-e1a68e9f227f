/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-0" }], ["path", { "d": "M3 9h3v6h-3z", "key": "svg-1" }], ["path", { "d": "M18 9h3v6h-3z", "key": "svg-2" }], ["path", { "d": "M3 5m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z", "key": "svg-3" }], ["path", { "d": "M12 5l0 14", "key": "svg-4" }]];
const IconSoccerField = createReactComponent("outline", "soccer-field", "SoccerField", __iconNode);

export { __iconNode, IconSoccerField as default };
//# sourceMappingURL=IconSoccerField.mjs.map
