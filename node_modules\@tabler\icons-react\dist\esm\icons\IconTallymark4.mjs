/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M6 5l0 14", "key": "svg-0" }], ["path", { "d": "M10 5l0 14", "key": "svg-1" }], ["path", { "d": "M14 5l0 14", "key": "svg-2" }], ["path", { "d": "M18 5l0 14", "key": "svg-3" }]];
const IconTallymark4 = createReactComponent("outline", "tallymark-4", "Tallymark4", __iconNode);

export { __iconNode, IconTallymark4 as default };
//# sourceMappingURL=IconTallymark4.mjs.map
