/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 16l4 -4l-4 -4", "key": "svg-0" }], ["path", { "d": "M8 12h8", "key": "svg-1" }], ["path", { "d": "M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z", "key": "svg-2" }]];
const IconSquareRoundedArrowRight = createReactComponent("outline", "square-rounded-arrow-right", "SquareRoundedArrowRight", __iconNode);

export { __iconNode, IconSquareRoundedArrowRight as default };
//# sourceMappingURL=IconSquareRoundedArrowRight.mjs.map
