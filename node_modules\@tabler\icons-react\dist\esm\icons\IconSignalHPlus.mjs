/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M7 16v-8", "key": "svg-0" }], ["path", { "d": "M11 8v8", "key": "svg-1" }], ["path", { "d": "M7 12h4", "key": "svg-2" }], ["path", { "d": "M14 12h4", "key": "svg-3" }], ["path", { "d": "M16 10v4", "key": "svg-4" }]];
const IconSignalHPlus = createReactComponent("outline", "signal-h-plus", "SignalHPlus", __iconNode);

export { __iconNode, IconSignalHPlus as default };
//# sourceMappingURL=IconSignalHPlus.mjs.map
