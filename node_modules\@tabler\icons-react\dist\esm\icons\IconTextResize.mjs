/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M5 5m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-0" }], ["path", { "d": "M19 5m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-1" }], ["path", { "d": "M5 19m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-2" }], ["path", { "d": "M19 19m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-3" }], ["path", { "d": "M5 7v10", "key": "svg-4" }], ["path", { "d": "M7 5h10", "key": "svg-5" }], ["path", { "d": "M7 19h10", "key": "svg-6" }], ["path", { "d": "M19 7v10", "key": "svg-7" }], ["path", { "d": "M10 10h4", "key": "svg-8" }], ["path", { "d": "M12 14v-4", "key": "svg-9" }]];
const IconTextResize = createReactComponent("outline", "text-resize", "TextResize", __iconNode);

export { __iconNode, IconTextResize as default };
//# sourceMappingURL=IconTextResize.mjs.map
