/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M10 13a2 2 0 1 0 4 0a2 2 0 0 0 -4 0", "key": "svg-0" }], ["path", { "d": "M8 21v-1a2 2 0 0 1 2 -2h4a2 2 0 0 1 2 2v1", "key": "svg-1" }], ["path", { "d": "M15 5a2 2 0 1 0 4 0a2 2 0 0 0 -4 0", "key": "svg-2" }], ["path", { "d": "M17 10h2a2 2 0 0 1 2 2v1", "key": "svg-3" }], ["path", { "d": "M5 5a2 2 0 1 0 4 0a2 2 0 0 0 -4 0", "key": "svg-4" }], ["path", { "d": "M3 13v-1a2 2 0 0 1 2 -2h2", "key": "svg-5" }]];
const IconUsersGroup = createReactComponent("outline", "users-group", "UsersGroup", __iconNode);

export { __iconNode, IconUsersGroup as default };
//# sourceMappingURL=IconUsersGroup.mjs.map
