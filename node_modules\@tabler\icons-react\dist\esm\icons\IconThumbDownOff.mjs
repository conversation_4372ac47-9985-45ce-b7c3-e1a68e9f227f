/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M7 13v-6m-3 -3a1 1 0 0 0 -1 1v7a1 1 0 0 0 1 1h3a4 4 0 0 1 4 4v1a2 2 0 1 0 4 0v-3m2 -2h1a2 2 0 0 0 2 -2l-1 -5c-.295 -1.26 -1.11 -2.076 -2 -2h-7c-.57 0 -1.102 .159 -1.556 .434", "key": "svg-0" }], ["path", { "d": "M3 3l18 18", "key": "svg-1" }]];
const IconThumbDownOff = createReactComponent("outline", "thumb-down-off", "ThumbDownOff", __iconNode);

export { __iconNode, IconThumbDownOff as default };
//# sourceMappingURL=IconThumbDownOff.mjs.map
