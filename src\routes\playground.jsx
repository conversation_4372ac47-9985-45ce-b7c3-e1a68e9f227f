import { createFileRoute } from "@tanstack/react-router";

export const Route = createFileRoute("/playground")({
  component: PlaygroundComponent,
});

function PlaygroundComponent() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Playground</h1>
        <p className="text-muted-foreground">
          Experiment with different features and test configurations.
        </p>
      </div>
      
      <div className="grid gap-6">
        <div className="rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
          <div className="grid gap-4 md:grid-cols-2">
            <button className="rounded-md border border-input bg-background px-4 py-2 text-sm hover:bg-accent hover:text-accent-foreground">
              Run Test
            </button>
            <button className="rounded-md border border-input bg-background px-4 py-2 text-sm hover:bg-accent hover:text-accent-foreground">
              Clear Console
            </button>
          </div>
        </div>
        
        <div className="rounded-lg border p-6">
          <h3 className="text-lg font-semibold mb-4">Console Output</h3>
          <div className="bg-muted rounded-md p-4 font-mono text-sm">
            <div className="text-green-600">$ Ready for testing...</div>
            <div className="text-muted-foreground">Waiting for commands...</div>
          </div>
        </div>
      </div>
    </div>
  );
}
