/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M19 22.5a4.75 4.75 0 0 1 3.5 -3.5a4.75 4.75 0 0 1 -3.5 -3.5a4.75 4.75 0 0 1 -3.5 3.5a4.75 4.75 0 0 1 3.5 3.5", "key": "svg-0" }], ["path", { "d": "M12 21h-7a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v7", "key": "svg-1" }], ["path", { "d": "M3 10h18", "key": "svg-2" }], ["path", { "d": "M10 3v18", "key": "svg-3" }]];
const IconTableSpark = createReactComponent("outline", "table-spark", "TableSpark", __iconNode);

export { __iconNode, IconTableSpark as default };
//# sourceMappingURL=IconTableSpark.mjs.map
