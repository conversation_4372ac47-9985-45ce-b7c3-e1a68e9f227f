import { createFileRoute } from "@tanstack/react-router";

export const Route = createFileRoute("/")({
  component: HomeComponent,
});

function HomeComponent() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Welcome</h1>
        <p className="text-muted-foreground">
          Select a section from the sidebar to get started.
        </p>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <div className="rounded-lg border p-6">
          <h3 className="font-semibold">Playground</h3>
          <p className="text-sm text-muted-foreground mt-2">
            Experiment with different features and configurations.
          </p>
        </div>
        
        <div className="rounded-lg border p-6">
          <h3 className="font-semibold">Models</h3>
          <p className="text-sm text-muted-foreground mt-2">
            Manage and configure your AI models.
          </p>
        </div>
        
        <div className="rounded-lg border p-6">
          <h3 className="font-semibold">Documentation</h3>
          <p className="text-sm text-muted-foreground mt-2">
            Browse through comprehensive documentation.
          </p>
        </div>
      </div>
    </div>
  );
}
