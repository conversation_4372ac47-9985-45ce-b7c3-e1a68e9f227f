/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M10 16v-8", "key": "svg-0" }], ["path", { "d": "M10 12h4", "key": "svg-1" }], ["path", { "d": "M14 8v8", "key": "svg-2" }], ["path", { "d": "M17 8v8h2a2 2 0 0 0 2 -2v-4a2 2 0 0 0 -2 -2z", "key": "svg-3" }], ["path", { "d": "M3 8v6a2 2 0 1 0 4 0v-6", "key": "svg-4" }]];
const IconUhd = createReactComponent("outline", "uhd", "Uhd", __iconNode);

export { __iconNode, IconUhd as default };
//# sourceMappingURL=IconUhd.mjs.map
