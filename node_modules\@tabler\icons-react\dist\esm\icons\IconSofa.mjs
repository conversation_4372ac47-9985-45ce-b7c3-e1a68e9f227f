/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M4 11a2 2 0 0 1 2 2v1h12v-1a2 2 0 1 1 4 0v5a1 1 0 0 1 -1 1h-18a1 1 0 0 1 -1 -1v-5a2 2 0 0 1 2 -2z", "key": "svg-0" }], ["path", { "d": "M4 11v-3a3 3 0 0 1 3 -3h10a3 3 0 0 1 3 3v3", "key": "svg-1" }], ["path", { "d": "M12 5v9", "key": "svg-2" }]];
const IconSofa = createReactComponent("outline", "sofa", "Sofa", __iconNode);

export { __iconNode, IconSofa as default };
//# sourceMappingURL=IconSofa.mjs.map
