/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M15 17h6", "key": "svg-0" }], ["path", { "d": "M15 12l3 4.5", "key": "svg-1" }], ["path", { "d": "M21 12l-3 4.5v4.5", "key": "svg-2" }], ["path", { "d": "M5 5m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-3" }], ["path", { "d": "M17 5m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-4" }], ["path", { "d": "M7 5h8", "key": "svg-5" }], ["path", { "d": "M7 5v8a3 3 0 0 0 3 3h1", "key": "svg-6" }]];
const IconTransactionYuan = createReactComponent("outline", "transaction-yuan", "TransactionYuan", __iconNode);

export { __iconNode, IconTransactionYuan as default };
//# sourceMappingURL=IconTransactionYuan.mjs.map
