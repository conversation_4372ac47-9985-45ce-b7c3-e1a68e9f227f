/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M6 8m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-0" }], ["path", { "d": "M13 12l5 0", "key": "svg-1" }], ["path", { "d": "M20 6h-6a1 1 0 0 0 -1 1v11", "key": "svg-2" }]];
const IconTemperatureFahrenheit = createReactComponent("outline", "temperature-fahrenheit", "TemperatureFahrenheit", __iconNode);

export { __iconNode, IconTemperatureFahrenheit as default };
//# sourceMappingURL=IconTemperatureFahrenheit.mjs.map
