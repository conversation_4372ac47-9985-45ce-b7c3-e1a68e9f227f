/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 21h4l13 -13a1.5 1.5 0 0 0 -4 -4l-13 13v4", "key": "svg-0" }], ["path", { "d": "M14.5 5.5l4 4", "key": "svg-1" }], ["path", { "d": "M12 8l-5 -5l-4 4l5 5", "key": "svg-2" }], ["path", { "d": "M7 8l-1.5 1.5", "key": "svg-3" }], ["path", { "d": "M16 12l5 5l-4 4l-5 -5", "key": "svg-4" }], ["path", { "d": "M16 17l-1.5 1.5", "key": "svg-5" }]];
const IconTools = createReactComponent("outline", "tools", "Tools", __iconNode);

export { __iconNode, IconTools as default };
//# sourceMappingURL=IconTools.mjs.map
