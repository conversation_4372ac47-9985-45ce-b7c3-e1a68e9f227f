/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 5a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-14z", "key": "svg-0" }], ["path", { "d": "M9 15.037l6 -6", "key": "svg-1" }], ["path", { "d": "M9 9.068v.014", "key": "svg-2" }], ["path", { "d": "M15 15.082v.016", "key": "svg-3" }]];
const IconSquarePercentage = createReactComponent("outline", "square-percentage", "SquarePercentage", __iconNode);

export { __iconNode, IconSquarePercentage as default };
//# sourceMappingURL=IconSquarePercentage.mjs.map
