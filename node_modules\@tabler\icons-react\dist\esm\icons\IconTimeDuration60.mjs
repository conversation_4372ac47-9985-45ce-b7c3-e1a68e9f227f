/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M14 10.5v3a1.5 1.5 0 0 0 3 0v-3a1.5 1.5 0 0 0 -3 0z", "key": "svg-0" }], ["path", { "d": "M11 9h-2a1 1 0 0 0 -1 1v4a1 1 0 0 0 1 1h1a1 1 0 0 0 1 -1v-1a1 1 0 0 0 -1 -1h-2", "key": "svg-1" }], ["path", { "d": "M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0", "key": "svg-2" }]];
const IconTimeDuration60 = createReactComponent("outline", "time-duration-60", "TimeDuration60", __iconNode);

export { __iconNode, IconTimeDuration60 as default };
//# sourceMappingURL=IconTimeDuration60.mjs.map
