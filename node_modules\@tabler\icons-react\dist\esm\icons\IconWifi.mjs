/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 18l.01 0", "key": "svg-0" }], ["path", { "d": "M9.172 15.172a4 4 0 0 1 5.656 0", "key": "svg-1" }], ["path", { "d": "M6.343 12.343a8 8 0 0 1 11.314 0", "key": "svg-2" }], ["path", { "d": "M3.515 9.515c4.686 -4.687 12.284 -4.687 17 0", "key": "svg-3" }]];
const IconWifi = createReactComponent("outline", "wifi", "Wifi", __iconNode);

export { __iconNode, IconWifi as default };
//# sourceMappingURL=IconWifi.mjs.map
