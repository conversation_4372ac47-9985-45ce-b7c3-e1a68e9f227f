/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M3 12h5v8h4v-16h4v8h5", "key": "svg-0" }]];
const IconWaveSquare = createReactComponent("outline", "wave-square", "WaveSquare", __iconNode);

export { __iconNode, IconWaveSquare as default };
//# sourceMappingURL=IconWaveSquare.mjs.map
