/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M11 4a1 1 0 1 0 2 0a1 1 0 0 0 -2 0", "key": "svg-0" }], ["path", { "d": "M6.5 21l3.5 -5", "key": "svg-1" }], ["path", { "d": "M5 11l7 -2", "key": "svg-2" }], ["path", { "d": "M16 21l-4 -7v-5l7 -4", "key": "svg-3" }]];
const IconStretching2 = createReactComponent("outline", "stretching-2", "Stretching2", __iconNode);

export { __iconNode, IconStretching2 as default };
//# sourceMappingURL=IconStretching2.mjs.map
