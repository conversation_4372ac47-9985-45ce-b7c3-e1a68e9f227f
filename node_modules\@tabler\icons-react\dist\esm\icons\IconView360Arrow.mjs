/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M17 15.328c2.414 -.718 4 -1.94 4 -3.328c0 -2.21 -4.03 -4 -9 -4s-9 1.79 -9 4s4.03 4 9 4", "key": "svg-0" }], ["path", { "d": "M9 13l3 3l-3 3", "key": "svg-1" }]];
const IconView360Arrow = createReactComponent("outline", "view-360-arrow", "View360Arrow", __iconNode);

export { __iconNode, IconView360Arrow as default };
//# sourceMappingURL=IconView360Arrow.mjs.map
