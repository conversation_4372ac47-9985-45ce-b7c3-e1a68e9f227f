/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M5 5m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-0" }], ["path", { "d": "M7 5h9.5a3.5 3.5 0 0 1 0 7h-9a3.5 3.5 0 0 0 0 7h13.5", "key": "svg-1" }], ["path", { "d": "M18 16l3 3l-3 3", "key": "svg-2" }]];
const IconSTurnRight = createReactComponent("outline", "s-turn-right", "STurnRight", __iconNode);

export { __iconNode, IconSTurnRight as default };
//# sourceMappingURL=IconSTurnRight.mjs.map
