/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M17 8v-3a1 1 0 0 0 -1 -1h-8m-3.413 .584a2 2 0 0 0 1.413 3.416h2m4 0h6a1 1 0 0 1 1 1v3", "key": "svg-0" }], ["path", { "d": "M19 19a1 1 0 0 1 -1 1h-12a2 2 0 0 1 -2 -2v-12", "key": "svg-1" }], ["path", { "d": "M16 12h4v4m-4 0a2 2 0 0 1 -2 -2", "key": "svg-2" }], ["path", { "d": "M3 3l18 18", "key": "svg-3" }]];
const IconWalletOff = createReactComponent("outline", "wallet-off", "WalletOff", __iconNode);

export { __iconNode, IconWalletOff as default };
//# sourceMappingURL=IconWalletOff.mjs.map
