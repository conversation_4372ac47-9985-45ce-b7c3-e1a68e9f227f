/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M5 3m0 2a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2z", "key": "svg-0" }], ["path", { "d": "M12 14m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0", "key": "svg-1" }], ["path", { "d": "M8 6h.01", "key": "svg-2" }], ["path", { "d": "M11 6h.01", "key": "svg-3" }], ["path", { "d": "M14 6h2", "key": "svg-4" }], ["path", { "d": "M8 14c1.333 -.667 2.667 -.667 4 0c1.333 .667 2.667 .667 4 0", "key": "svg-5" }]];
const IconWashMachine = createReactComponent("outline", "wash-machine", "WashMachine", __iconNode);

export { __iconNode, IconWashMachine as default };
//# sourceMappingURL=IconWashMachine.mjs.map
