/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M10 13.5a4 4 0 1 0 4 0v-8.5a2 2 0 0 0 -4 0v8.5", "key": "svg-0" }], ["path", { "d": "M10 9l4 0", "key": "svg-1" }]];
const IconTemperature = createReactComponent("outline", "temperature", "Temperature", __iconNode);

export { __iconNode, IconTemperature as default };
//# sourceMappingURL=IconTemperature.mjs.map
