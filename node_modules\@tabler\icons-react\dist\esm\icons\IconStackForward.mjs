/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

const __iconNode = [["path", { "d": "M12 5l-8 4l8 4l8 -4l-8 -4", "fill": "currentColor", "key": "svg-0" }], ["path", { "d": "M10 12l-6 3l8 4l8 -4l-6 -3", "key": "svg-1" }]];
const IconStackForward = createReactComponent("outline", "stack-forward", "StackForward", __iconNode);

export { __iconNode, IconStackForward as default };
//# sourceMappingURL=IconStackForward.mjs.map
